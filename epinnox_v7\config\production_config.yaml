# Epinnox v7 Production Configuration
# LIVE TRADING CONFIGURATION - USE WITH EXTREME CAUTION

# Environment settings
environment: "production"
dry_run: false  # LIVE TRADING ENABLED
debug_mode: false
log_level: "INFO"

# Production safety settings
production:
  require_confirmation: true
  enable_emergency_stop: true
  max_daily_loss_usd: 500  # Conservative daily loss limit
  circuit_breaker_enabled: true
  monitoring_enabled: true
  backup_enabled: true

# Database Configuration
database:
  type: sqlite
  path: data/epinnox_production.db
  echo: false
  backup_enabled: true
  backup_interval_hours: 6  # More frequent backups in production
  backup_retention_days: 90
  data_retention_days: 365  # Keep production data longer

# Exchange Configuration
exchanges:
  htx:
    enabled: true
    type: "htx"
    testnet: false  # LIVE TRADING
    api_key: "${HTX_API_KEY}"
    api_secret: "${HTX_API_SECRET}"
    rate_limit_enabled: true
    max_requests_per_minute: 100
    connection_timeout: 30
    retry_attempts: 3
    
  # Additional exchanges can be added here
  # binance:
  #   enabled: false
  #   type: "binance"
  #   testnet: false
  #   api_key: "${BINANCE_API_KEY}"
  #   api_secret: "${BINANCE_API_SECRET}"

# Trading Configuration
trading_symbols: ["BTC/USDT", "ETH/USDT"]  # Start with major pairs
max_concurrent_positions: 2  # Conservative position limit
position_size_pct: 1.0  # Very conservative 1% per position
max_position_size_pct: 2.0  # Maximum 2% per position
leverage: 2  # Conservative leverage for production
min_spread_pct: 0.002
trading_interval_seconds: 60  # Check every minute

# LLM Trading Engine Configuration
llm:
  enabled: true
  api_url: "http://localhost:1234/v1/completions"
  model_name: "Phi-3.1-mini-128k-instruct.Q4_K_M.gguf"
  max_tokens: 512
  temperature: 0.2  # Lower temperature for more conservative decisions
  timeout: 30
  retry_attempts: 3
  confidence_threshold: 0.70  # Higher confidence threshold for production
  
  # LLM decision parameters
  analysis_interval_seconds: 30
  market_data_lookback_hours: 24
  technical_indicators: ["sma", "ema", "rsi", "bollinger"]
  sentiment_analysis: true
  risk_assessment: true

# Risk Management (CRITICAL FOR PRODUCTION)
risk_limits:
  # Daily limits
  max_daily_loss_usd: 500
  max_daily_loss_pct: 2.0
  max_daily_trades: 20
  
  # Position limits
  max_drawdown_pct: 3.0  # Very conservative drawdown limit
  min_liquidation_buffer_pct: 10.0  # Large safety buffer
  max_margin_usage_pct: 50.0  # Conservative margin usage
  
  # Stop loss and take profit
  stop_loss_pct: 1.5  # Tight stop losses
  take_profit_pct: 3.0  # Conservative take profits
  trailing_stop_enabled: true
  
  # Consecutive loss protection
  max_consecutive_losses: 3  # Halt after 3 losses
  consecutive_loss_cooldown_minutes: 60
  
  # Volatility protection
  max_volatility_threshold: 0.05  # 5% volatility limit
  volatility_halt_duration_minutes: 30

# Circuit Breakers
circuit_breakers:
  enabled: true
  
  # Market conditions
  spread_halt_threshold_pct: 0.5  # Halt if spread > 0.5%
  volume_spike_threshold: 3.0  # Halt on 3x volume spikes
  price_gap_threshold_pct: 2.0  # Halt on 2% price gaps
  
  # System conditions
  latency_halt_threshold_ms: 1000  # Halt if latency > 1 second
  error_rate_threshold_pct: 10.0  # Halt if error rate > 10%
  
  # Recovery settings
  auto_recovery_enabled: false  # Manual recovery only in production
  recovery_check_interval_minutes: 15

# Monitoring and Alerting
monitoring:
  enabled: true
  health_check_interval_seconds: 30
  performance_tracking: true
  
  # Alerts
  alerts:
    enabled: true
    email_enabled: false  # Configure email settings if needed
    webhook_enabled: false  # Configure webhook if needed
    
    # Alert thresholds
    loss_alert_threshold_usd: 100
    profit_alert_threshold_usd: 200
    position_size_alert_threshold_pct: 1.5
    
  # Metrics collection
  metrics:
    collect_system_metrics: true
    collect_trading_metrics: true
    collect_performance_metrics: true
    retention_days: 30

# Security Configuration
security:
  encryption_enabled: true
  api_key_encryption: true
  session_timeout_minutes: 30
  max_failed_login_attempts: 3
  require_2fa: false  # Enable if 2FA is set up
  
  # Audit logging
  audit_logging: true
  log_all_trades: true
  log_all_decisions: true
  log_security_events: true

# Backup and Recovery
backup:
  enabled: true
  
  # Database backups
  database_backup_interval_hours: 6
  database_backup_retention_days: 90
  
  # Configuration backups
  config_backup_enabled: true
  config_backup_interval_hours: 24
  
  # Log backups
  log_backup_enabled: true
  log_backup_interval_days: 7
  log_backup_retention_days: 30
  
  # Backup verification
  verify_backups: true
  backup_verification_interval_hours: 24

# Performance Optimization
performance:
  # Data caching
  cache_enabled: true
  cache_ttl_seconds: 30
  max_cache_size_mb: 100
  
  # Connection pooling
  connection_pool_size: 5
  max_connections: 10
  connection_timeout_seconds: 30
  
  # Memory management
  memory_limit_mb: 512
  gc_threshold: 1000

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # File logging
  file_logging: true
  log_dir: "logs/production"
  max_file_size_mb: 50
  backup_count: 10
  
  # Specific log files
  files:
    main: "epinnox_production.log"
    trading: "trading_decisions.log"
    risk: "risk_events.log"
    security: "security_events.log"
    performance: "performance.log"
    errors: "errors.log"
  
  # Log rotation
  rotation_enabled: true
  rotation_interval: "midnight"
  rotation_backup_count: 30

# Development and Testing Overrides (for testing production config)
testing:
  # Override settings for testing
  dry_run_override: true  # Force dry run for testing
  reduced_position_sizes: true
  test_mode_indicators: true
  mock_exchange_data: false

# Emergency Procedures
emergency:
  # Emergency contacts (configure as needed)
  contacts:
    primary: "<EMAIL>"
    secondary: "<EMAIL>"
  
  # Emergency actions
  auto_stop_on_major_loss: true
  auto_stop_loss_threshold_usd: 1000
  emergency_liquidation_enabled: true
  
  # Recovery procedures
  manual_recovery_required: true
  recovery_checklist_required: true

# Compliance and Regulatory
compliance:
  # Trade reporting
  trade_reporting_enabled: true
  report_all_transactions: true
  
  # Data retention for compliance
  min_data_retention_years: 5
  audit_trail_enabled: true
  
  # Risk disclosure
  risk_warnings_enabled: true
  position_size_warnings: true
  leverage_warnings: true
