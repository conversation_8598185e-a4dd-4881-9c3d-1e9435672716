"""
Real-Time Data Manager for Epinnox v7 GUI

Manages live data feeds, caching, and updates for the trading interface.
"""

import asyncio
import threading
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QApplication
import json

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import get_logger
from auth.authentication import session_manager

logger = get_logger()


@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    price: float
    bid: float
    ask: float
    volume: float
    change_24h: float
    change_pct: float
    timestamp: datetime
    
    @property
    def spread(self) -> float:
        return self.ask - self.bid
    
    @property
    def spread_pct(self) -> float:
        return (self.spread / self.price) * 100 if self.price > 0 else 0


@dataclass
class PositionData:
    """Position data structure"""
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    margin_used: float
    liquidation_price: float
    timestamp: datetime
    
    @property
    def market_value(self) -> float:
        return self.size * self.current_price


@dataclass
class AccountData:
    """Account data structure"""
    balance: float
    equity: float
    margin_used: float
    margin_free: float
    margin_ratio: float
    unrealized_pnl: float
    daily_pnl: float
    total_pnl: float
    timestamp: datetime


@dataclass
class SystemStatus:
    """System status data structure"""
    trading_active: bool
    emergency_stop: bool
    connection_status: str
    last_update: datetime
    cpu_usage: float
    memory_usage: float
    network_latency: float
    errors_count: int
    warnings_count: int


class DataManager(QObject):
    """Manages real-time data for the GUI"""
    
    # Signals for data updates
    market_data_updated = pyqtSignal(str, dict)  # symbol, data
    position_updated = pyqtSignal(str, dict)     # symbol, position
    account_updated = pyqtSignal(dict)           # account data
    system_status_updated = pyqtSignal(dict)     # system status
    trade_executed = pyqtSignal(dict)            # trade data
    alert_triggered = pyqtSignal(str, str, str)  # level, title, message
    
    def __init__(self, exchange_manager=None):
        super().__init__()
        self.logger = get_logger()

        # Exchange manager for live data
        self.exchange_manager = exchange_manager

        # Data storage
        self.market_data: Dict[str, MarketData] = {}
        self.positions: Dict[str, PositionData] = {}
        self.account_data: Optional[AccountData] = None
        self.system_status: Optional[SystemStatus] = None
        
        # Update timers
        self.market_timer = QTimer()
        self.account_timer = QTimer()
        self.system_timer = QTimer()
        
        # Subscribers for data updates
        self.market_subscribers: List[Callable] = []
        self.position_subscribers: List[Callable] = []
        self.account_subscribers: List[Callable] = []
        self.system_subscribers: List[Callable] = []
        
        # Configuration
        self.update_intervals = {
            'market': 1000,    # 1 second
            'account': 5000,   # 5 seconds
            'system': 2000     # 2 seconds
        }
        
        self.setup_timers()
        self.logger.info("Data Manager initialized")
    
    def setup_timers(self):
        """Setup update timers"""
        # Market data timer
        self.market_timer.timeout.connect(self.update_market_data)
        self.market_timer.setInterval(self.update_intervals['market'])
        
        # Account data timer
        self.account_timer.timeout.connect(self.update_account_data)
        self.account_timer.setInterval(self.update_intervals['account'])
        
        # System status timer
        self.system_timer.timeout.connect(self.update_system_status)
        self.system_timer.setInterval(self.update_intervals['system'])
    
    def start_updates(self):
        """Start all data updates"""
        if not session_manager.is_authenticated():
            self.logger.warning("Cannot start updates - user not authenticated")
            return
        
        self.market_timer.start()
        self.account_timer.start()
        self.system_timer.start()
        self.logger.info("Data updates started")
    
    def stop_updates(self):
        """Stop all data updates"""
        self.market_timer.stop()
        self.account_timer.stop()
        self.system_timer.stop()
        self.logger.info("Data updates stopped")
    
    def update_market_data(self):
        """Update market data for all symbols using live exchange data"""
        try:
            # Use real trading symbols for futures
            symbols = ['BTC-USDT', 'ETH-USDT']  # HTX Linear Swap format

            for symbol in symbols:
                market_data = None

                # Try to get real market data from exchange
                if self.exchange_manager and self.exchange_manager.is_exchange_ready():
                    try:
                        # Get live market data from HTX
                        live_data = self.exchange_manager.get_market_data_sync(symbol)
                        if live_data:
                            market_data = MarketData(
                                symbol=symbol,
                                price=live_data.last,
                                bid=live_data.bid,
                                ask=live_data.ask,
                                volume=live_data.volume,
                                change_24h=live_data.change_24h,
                                change_pct=live_data.change_pct_24h,
                                timestamp=live_data.timestamp
                            )
                            self.logger.debug(f"Updated live market data for {symbol}: ${live_data.last:.2f}")
                    except Exception as e:
                        self.logger.warning(f"Failed to get live data for {symbol}: {str(e)}")

                # Fallback to simulated data if live data unavailable
                if not market_data:
                    if symbol in self.market_data:
                        current = self.market_data[symbol]
                        price_change = (hash(str(datetime.now().microsecond)) % 200 - 100) / 10000
                        new_price = current.price * (1 + price_change)
                    else:
                        new_price = 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 0.1

                    market_data = MarketData(
                        symbol=symbol,
                        price=new_price,
                        bid=new_price * 0.9995,
                        ask=new_price * 1.0005,
                        volume=1000000.0,
                        change_24h=new_price * 0.02,
                        change_pct=2.0,
                        timestamp=datetime.now()
                    )
                
                self.market_data[symbol] = market_data
                
                # Emit signal
                self.market_data_updated.emit(symbol, {
                    'symbol': symbol,
                    'price': new_price,
                    'bid': market_data.bid,
                    'ask': market_data.ask,
                    'spread': market_data.spread,
                    'spread_pct': market_data.spread_pct,
                    'volume': market_data.volume,
                    'change_24h': market_data.change_24h,
                    'change_pct': market_data.change_pct,
                    'timestamp': market_data.timestamp.isoformat()
                })
        
        except Exception as e:
            self.logger.error(f"Error updating market data: {str(e)}")
    
    def update_account_data(self):
        """Update account data using live exchange data"""
        try:
            # Try to get real account data from exchange
            balance = 39.09  # Default fallback
            equity = balance
            margin_used = 0.0
            margin_free = balance

            if self.exchange_manager and self.exchange_manager.is_exchange_ready():
                try:
                    # Get live balance from HTX
                    exchange = self.exchange_manager.primary_exchange
                    if hasattr(exchange, 'ccxt_exchange') and exchange.ccxt_exchange:
                        balance_data = exchange.ccxt_exchange.fetch_balance()
                        if balance_data and 'USDT' in balance_data:
                            usdt_balance = balance_data['USDT']
                            balance = usdt_balance.get('total', 39.09)
                            margin_free = usdt_balance.get('free', balance)
                            margin_used = usdt_balance.get('used', 0.0)
                            equity = balance
                            self.logger.debug(f"Updated live account balance: ${balance:.2f}")
                except Exception as e:
                    self.logger.warning(f"Failed to get live balance: {str(e)}")

            account_data = AccountData(
                balance=balance,
                equity=equity,
                margin_used=margin_used,
                margin_free=margin_free,
                margin_ratio=margin_used / balance if balance > 0 else 0,
                unrealized_pnl=0.0,  # Would need position data
                daily_pnl=0.0,       # Would need trade history
                total_pnl=0.0,       # Would need trade history
                timestamp=datetime.now()
            )
            
            self.account_data = account_data
            
            # Emit signal
            self.account_updated.emit({
                'balance': account_data.balance,
                'equity': account_data.equity,
                'margin_used': account_data.margin_used,
                'margin_free': account_data.margin_free,
                'margin_ratio': account_data.margin_ratio,
                'unrealized_pnl': account_data.unrealized_pnl,
                'daily_pnl': account_data.daily_pnl,
                'total_pnl': account_data.total_pnl,
                'timestamp': account_data.timestamp.isoformat()
            })
        
        except Exception as e:
            self.logger.error(f"Error updating account data: {str(e)}")
    
    def update_system_status(self):
        """Update system status"""
        try:
            import psutil
            
            # Get system metrics
            cpu_usage = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            system_status = SystemStatus(
                trading_active=True,  # Get from trading system
                emergency_stop=False,  # Get from risk guard
                connection_status='connected',
                last_update=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                network_latency=10.0,  # Simulate
                errors_count=0,
                warnings_count=0
            )
            
            self.system_status = system_status
            
            # Emit signal
            self.system_status_updated.emit({
                'trading_active': system_status.trading_active,
                'emergency_stop': system_status.emergency_stop,
                'connection_status': system_status.connection_status,
                'last_update': system_status.last_update.isoformat(),
                'cpu_usage': system_status.cpu_usage,
                'memory_usage': system_status.memory_usage,
                'network_latency': system_status.network_latency,
                'errors_count': system_status.errors_count,
                'warnings_count': system_status.warnings_count
            })
        
        except Exception as e:
            self.logger.error(f"Error updating system status: {str(e)}")
    
    def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get market data for symbol"""
        return self.market_data.get(symbol)
    
    def get_position(self, symbol: str) -> Optional[PositionData]:
        """Get position for symbol"""
        return self.positions.get(symbol)
    
    def get_account_data(self) -> Optional[AccountData]:
        """Get current account data"""
        return self.account_data
    
    def get_system_status(self) -> Optional[SystemStatus]:
        """Get current system status"""
        return self.system_status
    
    def trigger_alert(self, level: str, title: str, message: str):
        """Trigger an alert"""
        self.alert_triggered.emit(level, title, message)
        self.logger.warning(f"Alert [{level}] {title}: {message}")
    
    def simulate_trade_execution(self, trade_data: Dict[str, Any]):
        """Simulate trade execution (for testing)"""
        self.trade_executed.emit(trade_data)
        self.logger.info(f"Trade executed: {trade_data}")


# Global data manager instance
_data_manager: Optional[DataManager] = None


def get_data_manager(exchange_manager=None) -> DataManager:
    """Get global data manager instance"""
    global _data_manager

    if _data_manager is None:
        _data_manager = DataManager(exchange_manager)
    elif exchange_manager and not _data_manager.exchange_manager:
        # Update exchange manager if not set
        _data_manager.exchange_manager = exchange_manager
        _data_manager.logger.info("Exchange manager connected to data manager")

    return _data_manager
