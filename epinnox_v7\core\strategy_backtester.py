"""
Strategy Backtester for Epinnox v7

Comprehensive backtesting system that simulates trading over historical OHLCV data
using the full LLM-driven pipeline with rule-based strategy support.
"""
import asyncio
import json
import yaml
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict

from core.scalper_engine import ScalperEngine
from core.strategy_state import StrategyState
from core.risk_guard import RiskGuard
from core.llm_prompt_builder import LLMPromptBuilder
from core.llm_response_parser import LLMResponseParser
from execution.position_tracker import PositionTracker
from execution.trade_executor import TradeExecutor
from llm.performance_feedback import PerformanceFeedback
from data.trade_logger import TradeLogger
from utils.logger import get_logger
from utils.enums import ActionType, PositionSide, RiskLevel

@dataclass
class BacktestTrade:
    """Represents a single trade in the backtest"""
    timestamp: float
    symbol: str
    action: str
    entry_price: float
    exit_price: Optional[float] = None
    position_size: float = 0.0
    pnl: float = 0.0
    pnl_pct: float = 0.0
    confidence: float = 0.0
    reason: str = ""
    duration: float = 0.0  # in seconds
    max_adverse_excursion: float = 0.0
    max_favorable_excursion: float = 0.0

@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    # Basic metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0

    # PnL metrics
    total_pnl: float = 0.0
    total_pnl_pct: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0

    # Risk metrics
    max_drawdown: float = 0.0
    max_drawdown_pct: float = 0.0
    sharpe_ratio: float = 0.0
    calmar_ratio: float = 0.0

    # Time-based metrics
    avg_trade_duration: float = 0.0  # in minutes
    trades_per_day: float = 0.0

    # Strategy-specific
    signal_accuracy: float = 0.0
    signal_frequency: Dict[str, int] = None
    time_of_day_performance: Dict[str, float] = None

    # Equity curve data
    equity_curve: List[Tuple[float, float]] = None  # (timestamp, equity)
    trades: List[BacktestTrade] = None

class RuleEngine:
    """Evaluates YAML-based strategy rules"""

    def __init__(self, strategy_config: Dict):
        self.strategy = strategy_config
        self.logger = get_logger()

    def evaluate_conditions(self, market_data: Dict, indicators: Dict, position: Dict) -> Dict:
        """
        Evaluate strategy conditions and return trading decision

        Returns:
            Dict with action, confidence, and reasoning
        """
        try:
            rules = self.strategy.get('rules', {})

            # Evaluate entry conditions
            long_conditions = self._evaluate_condition_group(
                rules.get('long_entry', []), market_data, indicators, position
            )
            short_conditions = self._evaluate_condition_group(
                rules.get('short_entry', []), market_data, indicators, position
            )
            exit_conditions = self._evaluate_condition_group(
                rules.get('exit', []), market_data, indicators, position
            )

            # Determine action based on conditions
            if exit_conditions['met'] and position.get('size', 0) != 0:
                return {
                    'action': ActionType.CLOSE.value,
                    'confidence': exit_conditions['confidence'],
                    'reason': f"Exit conditions met: {exit_conditions['reasons']}"
                }
            elif long_conditions['met'] and position.get('side') != PositionSide.LONG.value:
                return {
                    'action': ActionType.LONG.value,
                    'confidence': long_conditions['confidence'],
                    'reason': f"Long entry: {long_conditions['reasons']}"
                }
            elif short_conditions['met'] and position.get('side') != PositionSide.SHORT.value:
                return {
                    'action': ActionType.SHORT.value,
                    'confidence': short_conditions['confidence'],
                    'reason': f"Short entry: {short_conditions['reasons']}"
                }
            else:
                return {
                    'action': ActionType.HOLD.value,
                    'confidence': 0.5,
                    'reason': "No conditions met"
                }

        except Exception as e:
            self.logger.error(f"Error evaluating strategy rules: {str(e)}")
            return {
                'action': ActionType.HOLD.value,
                'confidence': 0.0,
                'reason': f"Rule evaluation error: {str(e)}"
            }

    def _evaluate_condition_group(self, conditions: List[Dict], market_data: Dict,
                                 indicators: Dict, position: Dict) -> Dict:
        """Evaluate a group of conditions (AND logic)"""
        if not conditions:
            return {'met': False, 'confidence': 0.0, 'reasons': []}

        met_conditions = []
        confidence_scores = []
        reasons = []

        for condition in conditions:
            result = self._evaluate_single_condition(condition, market_data, indicators, position)
            if result['met']:
                met_conditions.append(True)
                confidence_scores.append(result['confidence'])
                reasons.append(result['reason'])
            else:
                met_conditions.append(False)

        # All conditions must be met (AND logic)
        all_met = all(met_conditions)
        avg_confidence = np.mean(confidence_scores) if confidence_scores else 0.0

        return {
            'met': all_met,
            'confidence': avg_confidence,
            'reasons': reasons
        }

    def _evaluate_single_condition(self, condition: Dict, market_data: Dict,
                                  indicators: Dict, position: Dict) -> Dict:
        """Evaluate a single condition"""
        try:
            condition_type = condition.get('type')

            if condition_type == 'price_above':
                value = market_data.get('price', 0)
                threshold = condition.get('value', 0)
                met = value > threshold
                confidence = min(1.0, (value - threshold) / threshold * 10) if met else 0.0

            elif condition_type == 'price_below':
                value = market_data.get('price', 0)
                threshold = condition.get('value', 0)
                met = value < threshold
                confidence = min(1.0, (threshold - value) / threshold * 10) if met else 0.0

            elif condition_type == 'indicator_above':
                indicator_name = condition.get('indicator')
                timeframe = condition.get('timeframe', '1m')
                value = indicators.get(timeframe, {}).get(indicator_name, 0)
                threshold = condition.get('value', 0)
                met = value > threshold
                confidence = min(1.0, abs(value - threshold) / threshold) if met else 0.0

            elif condition_type == 'indicator_below':
                indicator_name = condition.get('indicator')
                timeframe = condition.get('timeframe', '1m')
                value = indicators.get(timeframe, {}).get(indicator_name, 0)
                threshold = condition.get('value', 0)
                met = value < threshold
                confidence = min(1.0, abs(threshold - value) / threshold) if met else 0.0

            elif condition_type == 'volume_surge':
                volume_surge = market_data.get('volume_surge', 100)
                threshold = condition.get('value', 150)
                met = volume_surge > threshold
                confidence = min(1.0, (volume_surge - threshold) / threshold) if met else 0.0

            else:
                self.logger.warning(f"Unknown condition type: {condition_type}")
                return {'met': False, 'confidence': 0.0, 'reason': f"Unknown condition: {condition_type}"}

            return {
                'met': met,
                'confidence': confidence,
                'reason': f"{condition_type}: {value:.4f} vs {threshold:.4f}" if 'value' in locals() else condition_type
            }

        except Exception as e:
            self.logger.error(f"Error evaluating condition {condition}: {str(e)}")
            return {'met': False, 'confidence': 0.0, 'reason': f"Condition error: {str(e)}"}

class StrategyBacktester:
    """
    Comprehensive backtesting system for Epinnox v7

    Features:
    - Historical OHLCV data simulation
    - LLM-driven or rule-based strategy execution
    - Comprehensive performance metrics
    - Signal heatmap generation
    - Equity curve tracking
    - Risk-adjusted returns
    """

    def __init__(self, config: Dict, ohlcv_data: pd.DataFrame, symbol: str,
                 strategy_config: Optional[Dict] = None):
        self.config = config.copy()
        self.ohlcv_data = ohlcv_data.copy()
        self.symbol = symbol
        self.strategy_config = strategy_config
        self.logger = get_logger()

        # Initialize components
        self.strategy_state = StrategyState(config)
        self.risk_guard = RiskGuard(config)
        self.performance_feedback = PerformanceFeedback(config)

        # Initialize rule engine if strategy provided
        self.rule_engine = RuleEngine(strategy_config) if strategy_config else None

        # Simulation state
        self.current_position = {
            'side': PositionSide.NONE.value,
            'size': 0.0,
            'entry_price': 0.0,
            'unrealized_pnl': 0.0,
            'leverage': 1.0
        }

        # Results tracking
        self.trades: List[BacktestTrade] = []
        self.equity_curve: List[Tuple[float, float]] = []
        self.signal_frequency: Dict[str, int] = {action.value: 0 for action in ActionType}
        self.time_of_day_signals: Dict[int, List[str]] = {}  # hour -> [actions]

        # Performance tracking
        self.initial_balance = config.get('initial_balance', 10000.0)
        self.current_balance = self.initial_balance
        self.peak_balance = self.initial_balance
        self.max_drawdown = 0.0

        # Ensure required columns exist
        self._validate_data()

    def _validate_data(self):
        """Validate OHLCV data has required columns"""
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in self.ohlcv_data.columns]

        if missing_columns:
            raise ValueError(f"Missing required columns in OHLCV data: {missing_columns}")

        # Convert timestamp to datetime if needed
        if not pd.api.types.is_datetime64_any_dtype(self.ohlcv_data['timestamp']):
            self.ohlcv_data['timestamp'] = pd.to_datetime(self.ohlcv_data['timestamp'])

        # Sort by timestamp
        self.ohlcv_data = self.ohlcv_data.sort_values('timestamp').reset_index(drop=True)

        self.logger.info(f"Validated OHLCV data: {len(self.ohlcv_data)} candles from "
                        f"{self.ohlcv_data['timestamp'].iloc[0]} to {self.ohlcv_data['timestamp'].iloc[-1]}")

    async def run(self, use_llm: bool = False) -> BacktestResults:
        """
        Run the backtest simulation

        Parameters:
            use_llm: If True, use LLM for decisions. If False, use rule engine.

        Returns:
            BacktestResults with comprehensive metrics
        """
        self.logger.info(f"Starting backtest for {self.symbol} over {len(self.ohlcv_data)} candles...")
        self.logger.info(f"Strategy mode: {'LLM-driven' if use_llm else 'Rule-based'}")

        # Initialize LLM components if needed
        if use_llm:
            await self._initialize_llm_components()

        # Process each candle
        for idx, row in self.ohlcv_data.iterrows():
            try:
                await self._process_candle(idx, row, use_llm)

                # Update equity curve
                current_equity = self._calculate_current_equity(row['close'])
                self.equity_curve.append((row['timestamp'].timestamp(), current_equity))

                # Update peak and drawdown
                if current_equity > self.peak_balance:
                    self.peak_balance = current_equity

                current_drawdown = (self.peak_balance - current_equity) / self.peak_balance
                self.max_drawdown = max(self.max_drawdown, current_drawdown)

            except Exception as e:
                self.logger.error(f"Error processing candle {idx}: {str(e)}")
                continue

        # Calculate final results
        results = self._calculate_results()

        self.logger.info("Backtest complete.")
        self.logger.info(f"Total trades: {results.total_trades}")
        self.logger.info(f"Win rate: {results.win_rate:.2f}%")
        self.logger.info(f"Total PnL: {results.total_pnl:.2f} USDT ({results.total_pnl_pct:.2f}%)")
        self.logger.info(f"Max drawdown: {results.max_drawdown_pct:.2f}%")
        self.logger.info(f"Sharpe ratio: {results.sharpe_ratio:.2f}")

        return results

    async def _initialize_llm_components(self):
        """Initialize LLM components for decision making"""
        try:
            from llm.model_runner import ModelRunner

            self.model_runner = ModelRunner(self.config)
            self.prompt_builder = LLMPromptBuilder()
            self.response_parser = LLMResponseParser(self.config)

            await self.model_runner.initialize()
            self.logger.info("LLM components initialized for backtesting")

        except Exception as e:
            self.logger.error(f"Failed to initialize LLM components: {str(e)}")
            raise

    async def _process_candle(self, idx: int, row: pd.Series, use_llm: bool):
        """Process a single candle and make trading decisions"""
        try:
            # Create market data from candle
            market_data = self._create_market_data(row)

            # Update strategy state
            self.strategy_state.update(market_data)

            # Get trading decision
            if use_llm:
                decision = await self._get_llm_decision(market_data)
            else:
                decision = self._get_rule_based_decision(market_data)

            if not decision:
                return

            # Track signal frequency
            action = decision.get('action', ActionType.HOLD.value)
            self.signal_frequency[action] += 1

            # Track time-of-day patterns
            hour = row['timestamp'].hour
            if hour not in self.time_of_day_signals:
                self.time_of_day_signals[hour] = []
            self.time_of_day_signals[hour].append(action)

            # Validate decision with risk guard
            if not self._validate_decision(decision, market_data):
                return

            # Execute trade simulation
            await self._simulate_trade(decision, market_data, row['timestamp'])

        except Exception as e:
            self.logger.error(f"Error processing candle {idx}: {str(e)}")

    def _create_market_data(self, row: pd.Series) -> Dict:
        """Create market data dictionary from OHLCV row"""
        return {
            'symbol': self.symbol,
            'timestamp': row['timestamp'].timestamp(),
            'price': float(row['close']),
            'open': float(row['open']),
            'high': float(row['high']),
            'low': float(row['low']),
            'close': float(row['close']),
            'volume': float(row['volume']),
            'bid': float(row['close']) * 0.9995,  # Simulate bid/ask spread
            'ask': float(row['close']) * 1.0005,
            'spread_pct': 0.05,  # 0.05% spread simulation
            'volatility': self._calculate_candle_volatility(row),
            'delta_1m': 0.0,  # Would need more data for real calculation
            'delta_5m': 0.0,
            'buy_pressure_pct': 50.0,  # Neutral simulation
            'sell_wall_size': 0.0,
            'volume_surge': 100.0
        }

    def _calculate_candle_volatility(self, row: pd.Series) -> float:
        """Calculate volatility for a single candle"""
        try:
            high_low_range = (row['high'] - row['low']) / row['close']
            return float(high_low_range)
        except:
            return 0.0

    async def _get_llm_decision(self, market_data: Dict) -> Optional[Dict]:
        """Get trading decision from LLM"""
        try:
            # Build prompt
            prompt = self.prompt_builder.build_prompt(
                market_data=market_data,
                position_data=self.current_position,
                strategy_data=self.strategy_state.get_state(),
                performance_data=self.performance_feedback.get_metrics(),
                analysis={},  # Would need technical analysis in real implementation
                account_data=self._get_account_data(),
                opportunity_data=None
            )

            # Get model response
            response = await self.model_runner.get_trading_decision(prompt)
            if not response:
                return None

            # Parse response
            decision = await self.response_parser.parse_response(response, prompt)
            return decision

        except Exception as e:
            self.logger.error(f"Error getting LLM decision: {str(e)}")
            return None

    def _get_rule_based_decision(self, market_data: Dict) -> Optional[Dict]:
        """Get trading decision from rule engine"""
        if not self.rule_engine:
            return None

        try:
            # Simulate basic indicators (in real implementation, would use TechnicalAnalysis)
            indicators = self._simulate_indicators(market_data)

            # Get decision from rule engine
            decision = self.rule_engine.evaluate_conditions(
                market_data, indicators, self.current_position
            )

            # Add required fields for trade execution
            if decision['action'] in [ActionType.LONG.value, ActionType.SHORT.value]:
                decision.update({
                    'entry_price': market_data['price'],
                    'position_size': self._calculate_position_size(market_data),
                    'stop_loss': self._calculate_stop_loss(market_data, decision['action']),
                    'take_profit': self._calculate_take_profit(market_data, decision['action'])
                })

            return decision

        except Exception as e:
            self.logger.error(f"Error getting rule-based decision: {str(e)}")
            return None

    def _simulate_indicators(self, market_data: Dict) -> Dict:
        """Simulate basic technical indicators for rule evaluation"""
        # In a real implementation, this would use the TechnicalAnalysis class
        # For now, simulate basic indicators
        price = market_data['price']

        return {
            '1m': {
                'rsi': 50.0 + np.random.normal(0, 10),  # Simulate RSI around 50
                'macd': np.random.normal(0, 0.1),
                'bb_upper': price * 1.02,
                'bb_lower': price * 0.98,
                'ema_20': price * (1 + np.random.normal(0, 0.01)),
                'sma_50': price * (1 + np.random.normal(0, 0.02))
            }
        }

    def _calculate_position_size(self, market_data: Dict) -> float:
        """Calculate position size based on risk management"""
        try:
            # Use configured position size percentage
            position_size_pct = self.config.get('position_size_pct', 0.02)
            leverage = self.config.get('leverage', 1)

            # Calculate position size in base currency
            position_value = self.current_balance * position_size_pct * leverage
            position_size = position_value / market_data['price']

            return position_size

        except Exception as e:
            self.logger.error(f"Error calculating position size: {str(e)}")
            return 0.0

    def _calculate_stop_loss(self, market_data: Dict, action: str) -> float:
        """Calculate stop loss price"""
        price = market_data['price']
        sl_distance_pct = self.config.get('min_sl_distance_pct', 0.005)

        if action == ActionType.LONG.value:
            return price * (1 - sl_distance_pct)
        elif action == ActionType.SHORT.value:
            return price * (1 + sl_distance_pct)
        else:
            return price

    def _calculate_take_profit(self, market_data: Dict, action: str) -> float:
        """Calculate take profit price"""
        price = market_data['price']
        tp_distance_pct = self.config.get('min_sl_distance_pct', 0.005) * 2  # 2:1 RR

        if action == ActionType.LONG.value:
            return price * (1 + tp_distance_pct)
        elif action == ActionType.SHORT.value:
            return price * (1 - tp_distance_pct)
        else:
            return price
