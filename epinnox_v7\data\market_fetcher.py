import asyncio
from typing import Dict, List, Optional
import ccxt.async_support as ccxt
import numpy as np
import time
from datetime import datetime, timedelta
from utils.logger import get_logger, log_error
from data.technical_analysis import TechnicalAnalysis

class MarketFetcher:
    """Fetches and processes real-time market data"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Configure tracked symbols
        self.symbols = config.get('symbols', ['BTC/USDT:USDT'])  # Default to BTC
        self.primary_timeframes = config.get('timeframes', [1, 5, 15, 30, 60])
        
        # Initialize data caches per symbol
        self.price_cache = {symbol: {} for symbol in self.symbols}
        self.volume_cache = {symbol: {} for symbol in self.symbols}
        
        # Market quality metrics per symbol
        self.market_quality = {symbol: {} for symbol in self.symbols}
        
        # Technical analysis per symbol
        self.technical_analysis = {
            symbol: TechnicalAnalysis(config) for symbol in self.symbols
        }

        # Initialize caches
        self.orderbook_cache = {}
        self.last_update = {}
        
        # Get config values with defaults
        self.price_update_interval = self.config.get('price_update_interval', 1)  # seconds
        self.book_update_interval = self.config.get('book_update_interval', 2)   # seconds
        
        # Position size limits
        self.max_position_size = self.config.get('exchange', {}).get('max_position_size_usd', 100)
        self.min_position_size = self.config.get('exchange', {}).get('min_position_size_usd', 10)
        self.max_leverage = self.config.get('exchange', {}).get('max_leverage', 5)
        
        # Exchange will be initialized in initialize()
        self.exchange = None
        self.markets = {}
        self._markets_loaded = False  # Track if markets are loaded
        
    async def initialize(self):
        """Initialize exchange connection and load markets"""
        try:
            self.exchange = await self._initialize_exchange()
            await self._load_markets()
        except Exception as e:
            self.logger.error(f"Failed to initialize MarketFetcher: {str(e)}")
            raise
            
    async def _initialize_exchange(self) -> ccxt.Exchange:
        """Initialize exchange connection with API credentials"""
        try:
            exchange_id = self.config['exchange']['name']
            exchange_class = getattr(ccxt, exchange_id)
              # HTX specific configuration for USDT-margined futures
            options = {
                'defaultType': 'swap',  # Use swap for USDT futures
                'marginMode': 'cross',
                'hedgeMode': True,      # Enable hedge mode for both long/short positions
                'defaultContractType': 'swap',
                'defaultMarginMode': 'cross',
                'adjustForTimeDifference': True,
                'recvWindow': 10000    # Increase request window
            }
            
            exchange = exchange_class({
                'apiKey': self.config['exchange'].get('api_key', ''),
                'secret': self.config['exchange'].get('api_secret', ''),
                'enableRateLimit': True,
                'options': options
            })
            
            self.logger.info("Setting up HTX connection")
            return exchange
            
        except Exception as e:
            self.logger.error(f"Failed to initialize exchange: {str(e)}")
            raise
            
    async def _load_markets(self):
        """Load and validate markets"""
        try:
            self.logger.info("Loading markets...")
            self.markets = await self.exchange.load_markets()
            self.logger.info(f"Successfully loaded {len(self.markets)} markets")
            
            # Validate all symbols exist
            futures_markets = [m for m in self.markets.keys() if ':USDT' in m]
            
            for symbol in self.config.get('symbols', []):
                if symbol not in self.markets:
                    self.logger.warning(f"Symbol {symbol} not found in available markets!")
                    # Print available futures markets for debugging
                    examples = futures_markets[:5] if futures_markets else list(self.markets.keys())[:5]
                    self.logger.info(f"Available futures markets: {examples}")
                    self.logger.info(f"Total futures markets: {len(futures_markets)}")
                    raise ValueError(f"Symbol {symbol} not found in available markets")
            
            self._markets_loaded = True
            
        except Exception as e:
            self.logger.error(f"Failed to load markets: {str(e)}")
            raise
            
    async def get_market_data(self, symbol: str) -> Dict:
        """
        Get current market data including indicators
        
        Parameters
        ----------
        symbol : str
            Trading pair symbol
            
        Returns
        -------
        Dict
            Market data including price, indicators, and analysis
        """
        try:
            # Fetch current price data
            price_data = await self._fetch_price(symbol)
            if not price_data:
                return None
                
            # Get recent trades for volume analysis
            trades = await self._fetch_trades(symbol)
            
            # Get and analyze order book
            orderbook = await self._fetch_orderbook(symbol)
            book_analysis = self._analyze_orderbook(orderbook) if orderbook else {}
            
            # Calculate basic price metrics
            price_metrics = self._calculate_price_metrics(symbol, price_data)
            
            # Update technical analysis data
            self.ta.update_data(
                symbol=symbol,
                price=price_data['last'],
                volume=trades[-1]['amount'] if trades else 0,
                timestamp=int(datetime.now().timestamp() * 1000)
            )
            
            # Calculate technical indicators
            indicators = self.ta.calculate_indicators(symbol)
            
            # Prepare market data response
            market_data = {
                'symbol': symbol,
                'price': price_data.get('last'),
                'bid': price_data.get('bid'),
                'ask': price_data.get('ask'),
                'mark': price_data.get('mark'),  # Mark price for futures
                'spread_pct': (price_data.get('ask', 0) - price_data.get('bid', 0)) / price_data.get('bid', 1) * 100,
                'delta_1m': price_metrics.get('delta_1m', 0),
                'delta_5m': price_metrics.get('delta_5m', 0),
                'volatility': price_metrics.get('volatility', 0),
                'buy_pressure_pct': book_analysis.get('buy_pressure', 0),
                'sell_wall_size': book_analysis.get('sell_wall_size', 0),
                'volume_surge': self._analyze_volume(trades).get('volume_surge', 0)
            }
            
            # Add technical indicators for each timeframe
            market_data.update({
                'indicators': indicators
            })
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error fetching market data for {symbol}: {str(e)}")
            return None
            
    async def _fetch_price(self, symbol: str) -> Dict:
        """Fetch current price data"""
        try:
            # For HTX, we need to use mark price for futures
            ticker = await self.exchange.fetch_ticker(symbol)
            if not ticker:
                raise ValueError(f"No ticker data received for {symbol}")
                
            return {
                'last': float(ticker['last']) if ticker.get('last') else None,
                'bid': float(ticker['bid']) if ticker.get('bid') else None,
                'ask': float(ticker['ask']) if ticker.get('ask') else None,
                'mark': float(ticker.get('mark', ticker['last'])) if ticker.get('last') else None
            }
            
        except Exception as e:
            self.logger.error(f"Error fetching price for {symbol}: {str(e)}")
            return None
            
    async def _fetch_orderbook(self, symbol: str) -> Dict:
        """Fetch order book data"""
        try:
            # Check cache first
            if (
                symbol in self.orderbook_cache and
                datetime.now() - self.last_update.get(f"{symbol}_book", datetime.min) <
                timedelta(seconds=self.book_update_interval)
            ):
                return self.orderbook_cache[symbol]
            
            # Fetch fresh data
            orderbook = await self.exchange.fetch_order_book(symbol, limit=20)
            if not orderbook or not isinstance(orderbook, dict):
                raise ValueError(f"Invalid orderbook data for {symbol}")
                
            # Validate required fields
            if 'bids' not in orderbook or 'asks' not in orderbook:
                raise ValueError(f"Missing bids/asks in orderbook for {symbol}")
                
            # Update cache
            self.orderbook_cache[symbol] = orderbook
            self.last_update[f"{symbol}_book"] = datetime.now()
            
            return orderbook
            
        except Exception as e:
            self.logger.error(f"Error fetching orderbook for {symbol}: {str(e)}")
            return {'bids': [], 'asks': []}
            
    async def _fetch_recent_trades(self, symbol: str) -> List:
        """Fetch recent trades"""
        try:
            since = int((datetime.now() - timedelta(minutes=5)).timestamp() * 1000)
            trades = await self.exchange.fetch_trades(symbol, since=since, limit=100)
            
            if not trades or not isinstance(trades, list):
                raise ValueError(f"Invalid trades data for {symbol}")
                
            return trades
            
        except Exception as e:
            self.logger.error(f"Error fetching recent trades for {symbol}: {str(e)}")
            return []

    def _analyze_orderbook(self, orderbook: Dict) -> Dict:
        """Analyze order book for buy/sell pressure"""
        try:
            # Validate input
            if not orderbook or 'bids' not in orderbook or 'asks' not in orderbook:
                return {'buy_pressure': 50, 'sell_wall_size': 0}
                
            bids = np.array(orderbook['bids']) if orderbook['bids'] else np.array([[0, 0]])
            asks = np.array(orderbook['asks']) if orderbook['asks'] else np.array([[0, 0]])
            
            # Calculate total volume at top 5 levels safely
            bid_vol = np.sum(bids[:min(5, len(bids)), 1])
            ask_vol = np.sum(asks[:min(5, len(asks)), 1])
            
            # Calculate buy pressure as percentage
            total_vol = bid_vol + ask_vol
            buy_pressure = (bid_vol / total_vol * 100) if total_vol > 0 else 50
            
            # Find largest sell wall in top 10 levels
            sell_wall_size = np.max(asks[:min(10, len(asks)), 1]) if len(asks) > 0 else 0
            
            return {
                'buy_pressure': buy_pressure,
                'sell_wall_size': sell_wall_size
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing orderbook: {str(e)}")
            return {'buy_pressure': 50, 'sell_wall_size': 0}

    def _analyze_volume(self, trades: List) -> Dict:
        """Analyze recent trades volume"""
        try:
            if not trades:
                return {'volume_surge': 100}
                
            # Calculate volume in 1-minute windows safely
            volumes = []
            now = datetime.now()
            
            for trade in trades:
                if not isinstance(trade, dict) or 'timestamp' not in trade or 'amount' not in trade:
                    continue
                    
                trade_time = datetime.fromtimestamp(trade['timestamp']/1000)
                if now - trade_time < timedelta(minutes=1):
                    volumes.append(trade['amount'])
                    
            recent_vol = np.sum(volumes) if volumes else 0
            all_amounts = [t.get('amount', 0) for t in trades if isinstance(t, dict)]
            avg_vol = np.mean(all_amounts) * (len(volumes) or 1) if all_amounts else 1
            
            return {
                'volume_surge': (recent_vol / avg_vol * 100) if avg_vol > 0 else 100
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing volume: {str(e)}")
            return {'volume_surge': 100}
        
    def _calculate_price_metrics(self, symbol: str, price_data: Dict) -> Dict:
        """
        Calculate price change and volatility metrics
        
        Parameters
        ----------
        symbol : str
            Trading pair symbol
        price_data : Dict
            Price data from exchange
            
        Returns
        -------
        Dict
            Price metrics including deltas and volatility
        """
        try:
            current_price = price_data.get('last')
            if current_price is None:
                self.logger.warning(f"No valid price data for {symbol}")
                return {'delta_1m': 0, 'delta_5m': 0, 'volatility': 0}
            
            # Initialize price cache for symbol if needed
            if symbol not in self.price_cache:
                self.price_cache[symbol] = []
                self.logger.info(f"Initialized price cache for {symbol}")
            
            # Add new price
            self.price_cache[symbol].append(float(current_price))
            
            # Keep last 300 prices (5 minutes at 1s intervals)
            self.price_cache[symbol] = self.price_cache[symbol][-300:]
            
            # Convert to numpy array for calculations
            prices = np.array(self.price_cache[symbol])
            
            # Calculate metrics with safety checks
            delta_1m = 0
            delta_5m = 0
            volatility = 0
            
            if len(prices) >= 60:  # At least 1 minute of data
                delta_1m = ((current_price / prices[-60]) - 1) * 100
                
            if len(prices) >= 300:  # At least 5 minutes of data
                delta_5m = ((current_price / prices[0]) - 1) * 100
                
            # Calculate volatility only if we have enough data points (at least 30)
            if len(prices) >= 30:
                try:
                    returns = np.diff(prices) / prices[:-1]
                    # Filter out NaN and inf values
                    returns = returns[np.isfinite(returns)]
                    if len(returns) > 1:
                        volatility = float(np.std(returns, ddof=1) * np.sqrt(len(returns))) 
                except Exception as e:
                    self.logger.warning(f"Error calculating volatility: {e}")
                    volatility = 0
            
            return {
                'delta_1m': delta_1m,
                'delta_5m': delta_5m,
                'volatility': volatility
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating price metrics for {symbol}: {str(e)}")
            return {'delta_1m': 0, 'delta_5m': 0, 'volatility': 0}
        
    async def update_market_data(self) -> Dict[str, Dict]:
        """Update market data for all tracked symbols"""
        market_data = {}
        
        for symbol in self.symbols:
            try:
                data = await self._fetch_symbol_data(symbol)
                if data:
                    market_data[symbol] = data
                    self._update_market_quality(symbol, data)
            except Exception as e:
                self.logger.error(f"Error fetching data for {symbol}: {str(e)}")
                
        return market_data

    def _update_market_quality(self, symbol: str, data: Dict):
        """Update market quality metrics for symbol selection"""
        quality_score = 0.0
        metrics = {}
        
        # Volume quality (0-1)
        avg_volume = np.mean([d.get('volume', 0) for d in data.values()])
        volume_consistency = 1 - (np.std([d.get('volume', 0) for d in data.values()]) / avg_volume if avg_volume > 0 else 1)
        metrics['volume_quality'] = min(1.0, volume_consistency)
        quality_score += metrics['volume_quality'] * 0.2
        
        # Spread quality (0-1)
        spread = data.get('spread_pct', 100)
        metrics['spread_quality'] = max(0, 1 - (spread / 0.1))  # 0.1% spread as baseline
        quality_score += metrics['spread_quality'] * 0.2
        
        # Manipulation safety (0-1)
        tech_analysis = self.technical_analysis[symbol]
        manipulation_metrics = tech_analysis.calculate_indicators(symbol).get('manipulation_metrics', {})
        metrics['manipulation_safety'] = 1 - manipulation_metrics.get('overall_score', 1.0)
        quality_score += metrics['manipulation_safety'] * 0.3
        
        # Opportunity score (0-1)
        volatility = self._calculate_volatility(data)
        metrics['opportunity_score'] = min(1.0, volatility / 0.02)  # 2% volatility as baseline
        quality_score += metrics['opportunity_score'] * 0.3
        
        # Update market quality cache
        self.market_quality[symbol] = {
            'total_score': quality_score,
            'metrics': metrics,
            'timestamp': time.time()
        }

    def _calculate_volatility(self, data: Dict) -> float:
        """Calculate recent volatility for opportunity scoring"""
        if not data or 'close' not in data:
            return 0.0
            
        prices = data['close']
        if len(prices) < 2:
            return 0.0
            
        returns = np.diff(np.log(prices))
        return np.std(returns) * np.sqrt(len(returns))

    def get_best_trading_opportunities(self, min_quality: float = 0.7) -> List[Dict]:
        """Get list of symbols sorted by trading quality"""
        opportunities = []
        
        for symbol in self.symbols:
            quality = self.market_quality.get(symbol, {})
            if quality.get('total_score', 0) >= min_quality:
                opportunities.append({
                    'symbol': symbol,
                    'score': quality.get('total_score', 0),
                    'metrics': quality.get('metrics', {}),
                    'last_update': quality.get('timestamp', 0)
                })
        
        # Sort by quality score descending
        opportunities.sort(key=lambda x: x['score'], reverse=True)
        return opportunities

    async def _fetch_symbol_data(self, symbol: str) -> Optional[Dict]:
        """Fetch market data for a single symbol"""
        try:
            # Fetch current price data
            price_data = await self._fetch_price(symbol)
            if not price_data:
                return None
                
            # Get recent trades for volume analysis
            trades = await self._fetch_trades(symbol)
            
            # Get and analyze order book
            orderbook = await self._fetch_orderbook(symbol)
            book_analysis = self._analyze_orderbook(orderbook) if orderbook else {}
            
            # Calculate basic price metrics
            price_metrics = self._calculate_price_metrics(symbol, price_data)
            
            # Update technical analysis data
            self.technical_analysis[symbol].update_data(
                symbol=symbol,
                price=price_data['last'],
                volume=trades[-1]['amount'] if trades else 0,
                timestamp=int(datetime.now().timestamp() * 1000)
            )
            
            # Calculate technical indicators
            indicators = self.technical_analysis[symbol].calculate_indicators(symbol)
            
            # Prepare market data response
            market_data = {
                'symbol': symbol,
                'price': price_data.get('last'),
                'bid': price_data.get('bid'),
                'ask': price_data.get('ask'),
                'mark': price_data.get('mark'),  # Mark price for futures
                'spread_pct': (price_data.get('ask', 0) - price_data.get('bid', 0)) / price_data.get('bid', 1) * 100,
                'delta_1m': price_metrics.get('delta_1m', 0),
                'delta_5m': price_metrics.get('delta_5m', 0),
                'volatility': price_metrics.get('volatility', 0),
                'buy_pressure_pct': book_analysis.get('buy_pressure', 0),
                'sell_wall_size': book_analysis.get('sell_wall_size', 0),
                'volume_surge': self._analyze_volume(trades).get('volume_surge', 0)
            }
            
            # Add technical indicators for each timeframe
            market_data.update({
                'indicators': indicators
            })
            
            return market_data
            
        except Exception as e:
            self.logger.error(f"Error fetching {symbol} data: {str(e)}")
            return None
            
    async def close(self):
        """Clean up exchange connection"""
        await self.exchange.close()
