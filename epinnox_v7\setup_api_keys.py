#!/usr/bin/env python3
"""
API Key Setup for Epinnox v7 Production Trading

Secure setup and storage of exchange API credentials.
"""

import sys
import os
import getpass
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from security.key_manager import CredentialManager
from utils.logger import get_logger, setup_logger
from utils.config_validator import ConfigValidator

# Setup logging
try:
    config = {'logging': {'level': 'INFO'}}
    setup_logger(config)
except:
    pass  # Use default logging if setup fails
logger = get_logger()


class APIKeySetup:
    """Secure API key setup and management"""
    
    def __init__(self):
        self.logger = get_logger()
        self.credential_manager = None
    
    def setup_api_keys(self):
        """Interactive API key setup"""
        print("🔐 Epinnox v7 API Key Setup")
        print("=" * 40)
        print()
        print("This tool will securely store your exchange API credentials.")
        print("Your API keys will be encrypted and stored locally.")
        print()
        
        try:
            # Load configuration
            config = ConfigValidator.load_and_validate("config/production_config.yaml")
            
            # Initialize credential manager
            self.credential_manager = CredentialManager(config)
            
            # Setup HTX API keys
            self.setup_htx_keys()
            
            # Setup additional exchanges if needed
            self.setup_additional_exchanges()
            
            # Verify API keys
            self.verify_api_keys()
            
            print("\n✅ API key setup completed successfully!")
            print("Your credentials are now securely stored and encrypted.")
            
        except Exception as e:
            self.logger.error(f"API key setup failed: {str(e)}")
            print(f"\n❌ Setup failed: {str(e)}")
            return False
        
        return True
    
    def setup_htx_keys(self):
        """Setup HTX (Huobi) API keys"""
        print("\n📊 HTX (Huobi) API Key Setup")
        print("-" * 30)
        
        print("\nTo get your HTX API keys:")
        print("1. Log in to your HTX account")
        print("2. Go to Account > API Management")
        print("3. Create a new API key with trading permissions")
        print("4. Copy the API Key and Secret Key")
        print()
        print("⚠️ IMPORTANT: Enable only the following permissions:")
        print("   - Read account information")
        print("   - Trade (spot trading)")
        print("   - DO NOT enable withdrawal permissions")
        print()
        
        # Get API credentials
        api_key = getpass.getpass("Enter HTX API Key: ").strip()
        if not api_key:
            raise ValueError("API Key is required")
        
        api_secret = getpass.getpass("Enter HTX API Secret: ").strip()
        if not api_secret:
            raise ValueError("API Secret is required")
        
        # Validate format
        if len(api_key) < 20:
            raise ValueError("API Key appears to be too short")
        
        if len(api_secret) < 20:
            raise ValueError("API Secret appears to be too short")
        
        # Store credentials
        credentials = {
            'api_key': api_key,
            'api_secret': api_secret,
            'exchange': 'htx',
            'permissions': ['read', 'trade'],
            'created_at': str(datetime.now())
        }
        
        self.credential_manager.store_exchange_credentials('htx', credentials)
        print("✅ HTX API keys stored securely")
    
    def setup_additional_exchanges(self):
        """Setup additional exchange API keys"""
        print("\n🔄 Additional Exchanges")
        print("-" * 25)
        
        setup_more = input("Do you want to setup additional exchanges? (y/n): ").lower()
        
        if setup_more == 'y':
            print("\nSupported exchanges:")
            print("1. Binance (coming soon)")
            print("2. Bybit (coming soon)")
            print("3. OKX (coming soon)")
            print()
            print("Additional exchange support will be added in future updates.")
            print("For now, HTX is the primary supported exchange.")
    
    def verify_api_keys(self):
        """Verify stored API keys"""
        print("\n🔍 Verifying API Keys...")
        print("-" * 25)
        
        try:
            # Test HTX credentials
            htx_creds = self.credential_manager.get_exchange_credentials('htx')
            
            if htx_creds:
                print("✅ HTX credentials retrieved successfully")
                
                # Basic validation
                if 'api_key' in htx_creds and 'api_secret' in htx_creds:
                    print("✅ HTX credentials format is valid")
                else:
                    print("❌ HTX credentials format is invalid")
                    return False
            else:
                print("❌ HTX credentials not found")
                return False
            
            print("✅ All API keys verified successfully")
            return True
        
        except Exception as e:
            print(f"❌ API key verification failed: {str(e)}")
            return False
    
    def show_security_recommendations(self):
        """Show security recommendations"""
        print("\n🛡️ Security Recommendations")
        print("=" * 30)
        print()
        print("1. API Key Permissions:")
        print("   ✅ Enable: Read, Trade")
        print("   ❌ Disable: Withdraw, Transfer")
        print()
        print("2. IP Restrictions:")
        print("   • Add your server's IP address to the whitelist")
        print("   • This prevents unauthorized access from other IPs")
        print()
        print("3. Regular Security Checks:")
        print("   • Monitor your API key usage regularly")
        print("   • Rotate API keys periodically")
        print("   • Check for any unauthorized activity")
        print()
        print("4. Backup:")
        print("   • Keep a secure backup of your API keys")
        print("   • Store them in a password manager")
        print("   • Never share them with anyone")
        print()
        print("5. Monitoring:")
        print("   • Enable email notifications for API usage")
        print("   • Monitor your account balance regularly")
        print("   • Set up alerts for large trades")
        print()


def main():
    """Main entry point"""
    print("🔐 Epinnox v7 API Key Setup Tool")
    print("=" * 40)
    
    # Check if production config exists
    if not Path("config/production_config.yaml").exists():
        print("\n❌ Production configuration not found!")
        print("Please create config/production_config.yaml first.")
        print("You can copy from config/scalper_config.yaml and modify for production.")
        return False
    
    # Create setup instance
    setup = APIKeySetup()
    
    try:
        # Show security recommendations first
        setup.show_security_recommendations()
        
        # Confirm user wants to proceed
        proceed = input("\nDo you want to proceed with API key setup? (y/n): ").lower()
        if proceed != 'y':
            print("Setup cancelled by user.")
            return False
        
        # Run setup
        success = setup.setup_api_keys()
        
        if success:
            print("\n🎉 API Key Setup Complete!")
            print()
            print("Next steps:")
            print("1. Run the pre-launch checklist: python -m production.pre_launch_checklist")
            print("2. Test the system in dry-run mode first")
            print("3. When ready, launch production: python launch_production.py")
            print()
            print("⚠️ Remember to test thoroughly before live trading!")
        
        return success
    
    except KeyboardInterrupt:
        print("\n\n🛑 Setup interrupted by user")
        return False
    
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        return False


if __name__ == "__main__":
    from datetime import datetime
    success = main()
    sys.exit(0 if success else 1)
