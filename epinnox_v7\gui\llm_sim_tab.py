"""
Live LLM Simulation Tab for Epinnox v7 Dashboard

This module provides real-time LLM decision simulation and monitoring including:
- Live market data streaming
- LLM decision prediction every X seconds
- Prediction accuracy tracking vs actual price movements
- Performance comparison over different time horizons
- Simulation results logging and analysis
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime, timedelta

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QGridLayout,
    QPushButton, QLabel, QTableWidget, QTableWidgetItem, QTextEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QProgressBar,
    QTabWidget, QScrollArea, QMessageBox
)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QColor

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import pandas as pd
import numpy as np

from strategy_builder.backtester import Backtester
from core.llm_prompt_builder import LLMPromptBuilder
from core.llm_response_parser import LLMResponseParser
from core.strategy_state import StrategyState
from llm.performance_feedback import PerformanceFeedback
from utils.logger import get_logger
from utils.enums import ActionType


class LLMSimulationWorker(QThread):
    """Worker thread for running live LLM simulation"""
    
    prediction_made = pyqtSignal(dict)  # prediction data
    error = pyqtSignal(str)
    status_update = pyqtSignal(str)
    
    def __init__(self, symbol, interval_seconds, config):
        super().__init__()
        self.symbol = symbol
        self.interval_seconds = interval_seconds
        self.config = config
        self.logger = get_logger()
        self.should_stop = False
        
        # Initialize components
        self.prompt_builder = LLMPromptBuilder()
        self.response_parser = LLMResponseParser(config)
        self.strategy_state = StrategyState(config)
        self.performance_feedback = PerformanceFeedback(config)
    
    def stop(self):
        """Stop the simulation"""
        self.should_stop = True
    
    def run(self):
        """Run LLM simulation in separate thread"""
        try:
            # Create event loop for async operations
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run simulation
            loop.run_until_complete(self._run_simulation())
            
        except Exception as e:
            self.logger.error(f"LLM simulation error: {str(e)}")
            self.error.emit(str(e))
        finally:
            try:
                loop.close()
            except:
                pass
    
    async def _run_simulation(self):
        """Execute live LLM simulation"""
        backtester = Backtester()
        
        self.status_update.emit("Starting LLM simulation...")
        
        while not self.should_stop:
            try:
                # Fetch recent market data
                self.status_update.emit("Fetching market data...")
                data = await backtester.fetch_historical_data(
                    symbol=self.symbol,
                    timeframe='1m',
                    days=0.1,  # Last few hours
                    exchange_id='binance'
                )
                
                if data is None or len(data) < 10:
                    self.status_update.emit("Insufficient market data")
                    await asyncio.sleep(self.interval_seconds)
                    continue
                
                # Get current market state
                current_candle = data.iloc[-1]
                market_data = self._create_market_data(current_candle)
                
                # Update strategy state
                self._update_strategy_state(market_data, data)
                
                # Get LLM prediction
                self.status_update.emit("Getting LLM prediction...")
                prediction = await self._get_llm_prediction(market_data)
                
                if prediction:
                    # Store prediction with timestamp
                    prediction_data = {
                        'timestamp': datetime.now().timestamp(),
                        'symbol': self.symbol,
                        'current_price': market_data['price'],
                        'prediction': prediction,
                        'market_data': market_data
                    }
                    
                    self.prediction_made.emit(prediction_data)
                    
                    # Log prediction
                    self._log_prediction(prediction_data)
                
                self.status_update.emit(f"Next prediction in {self.interval_seconds}s...")
                await asyncio.sleep(self.interval_seconds)
                
            except Exception as e:
                self.logger.error(f"Error in simulation loop: {str(e)}")
                self.error.emit(str(e))
                await asyncio.sleep(self.interval_seconds)
    
    def _create_market_data(self, candle_row) -> Dict:
        """Create market data from candle"""
        return {
            'symbol': self.symbol,
            'timestamp': candle_row['timestamp'].timestamp(),
            'price': float(candle_row['close']),
            'open': float(candle_row['open']),
            'high': float(candle_row['high']),
            'low': float(candle_row['low']),
            'close': float(candle_row['close']),
            'volume': float(candle_row['volume']),
            'bid': float(candle_row['close']) * 0.9995,
            'ask': float(candle_row['close']) * 1.0005,
            'spread_pct': 0.05,
            'volatility': (candle_row['high'] - candle_row['low']) / candle_row['close'],
            'delta_1m': 0.0,
            'delta_5m': 0.0,
            'buy_pressure_pct': 50.0,
            'sell_wall_size': 0.0,
            'volume_surge': 100.0
        }
    
    def _update_strategy_state(self, market_data: Dict, historical_data: pd.DataFrame):
        """Update strategy state with current market data"""
        try:
            # Get recent price and volume data
            recent_prices = historical_data['close'].tail(20).tolist()
            recent_volumes = historical_data['volume'].tail(20).tolist()
            
            # Update market regime
            self.strategy_state.update_market_regime(recent_prices, recent_volumes)
            
            # Update risk level (simplified for simulation)
            self.strategy_state.update_risk_level(0.0, 0.5, market_data['volatility'])
            
        except Exception as e:
            self.logger.error(f"Error updating strategy state: {str(e)}")
    
    async def _get_llm_prediction(self, market_data: Dict) -> Optional[Dict]:
        """Get LLM trading prediction"""
        try:
            # Simulate position data (no actual position in simulation)
            position_data = {
                'side': 'none',
                'size': 0.0,
                'entry_price': 0.0,
                'unrealized_pnl': 0.0
            }
            
            # Simulate account data
            account_data = {
                'balance': 1000.0,
                'equity': 1000.0,
                'margin_used': 0.0,
                'margin_available': 1000.0
            }
            
            # Build prompt
            prompt = self.prompt_builder.build_prompt(
                market_data=market_data,
                position_data=position_data,
                strategy_data=self.strategy_state.get_state(),
                performance_data=self.performance_feedback.get_metrics(),
                analysis={},
                account_data=account_data,
                opportunity_data=None
            )
            
            # For simulation, we'll create a mock LLM response
            # In a real implementation, this would call the actual LLM
            mock_response = self._generate_mock_llm_response(market_data)
            
            # Parse response
            prediction = await self.response_parser.parse_response(mock_response, prompt)
            return prediction
            
        except Exception as e:
            self.logger.error(f"Error getting LLM prediction: {str(e)}")
            return None
    
    def _generate_mock_llm_response(self, market_data: Dict) -> str:
        """Generate mock LLM response for simulation"""
        # This simulates an LLM response based on simple market conditions
        price = market_data['price']
        volatility = market_data['volatility']
        
        # Simple logic for demonstration
        if volatility > 0.01:  # High volatility
            if np.random.random() > 0.5:
                action = "LONG"
                confidence = 0.7
                reason = "High volatility with upward momentum detected"
            else:
                action = "SHORT"
                confidence = 0.7
                reason = "High volatility with downward pressure"
        else:
            action = "HOLD"
            confidence = 0.5
            reason = "Low volatility, waiting for clearer signals"
        
        return json.dumps({
            "action": action,
            "confidence": confidence,
            "reasoning": reason,
            "entry_price": price,
            "stop_loss": price * (0.995 if action == "LONG" else 1.005),
            "take_profit": price * (1.01 if action == "LONG" else 0.99)
        })
    
    def _log_prediction(self, prediction_data: Dict):
        """Log prediction to file"""
        try:
            log_dir = Path("logs/sim_llm_results")
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # Create daily log file
            date_str = datetime.now().strftime("%Y%m%d")
            log_file = log_dir / f"llm_sim_{self.symbol.replace('/', '_')}_{date_str}.json"
            
            # Load existing data or create new
            if log_file.exists():
                with open(log_file, 'r') as f:
                    data = json.load(f)
            else:
                data = {"predictions": []}
            
            # Add new prediction
            data["predictions"].append(prediction_data)
            
            # Save updated data
            with open(log_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
        except Exception as e:
            self.logger.error(f"Error logging prediction: {str(e)}")


class PredictionAccuracyWidget(QWidget):
    """Widget for displaying prediction accuracy analysis"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.predictions = []
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        
        # Create matplotlib figure
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # Initialize empty plots
        self.ax1 = self.figure.add_subplot(211)
        self.ax2 = self.figure.add_subplot(212)
        
        self.ax1.set_title("LLM Predictions vs Actual Price Movement")
        self.ax1.set_ylabel("Price")
        self.ax1.grid(True, alpha=0.3)
        
        self.ax2.set_title("Prediction Accuracy Over Time")
        self.ax2.set_xlabel("Time")
        self.ax2.set_ylabel("Accuracy %")
        self.ax2.grid(True, alpha=0.3)
        
        self.canvas.draw()
    
    def add_prediction(self, prediction_data: Dict):
        """Add new prediction to tracking"""
        self.predictions.append(prediction_data)
        
        # Keep only last 100 predictions for performance
        if len(self.predictions) > 100:
            self.predictions = self.predictions[-100:]
        
        self.update_plots()
    
    def update_plots(self):
        """Update accuracy plots"""
        try:
            if len(self.predictions) < 2:
                return
            
            self.ax1.clear()
            self.ax2.clear()
            
            # Extract data
            timestamps = [datetime.fromtimestamp(p['timestamp']) for p in self.predictions]
            prices = [p['current_price'] for p in self.predictions]
            actions = [p['prediction'].get('action', 'HOLD') for p in self.predictions]
            
            # Plot price movement
            self.ax1.plot(timestamps, prices, 'b-', linewidth=2, label='Price')
            
            # Mark predictions
            for i, (ts, price, action) in enumerate(zip(timestamps, prices, actions)):
                color = 'green' if action == 'LONG' else 'red' if action == 'SHORT' else 'gray'
                marker = '^' if action == 'LONG' else 'v' if action == 'SHORT' else 'o'
                self.ax1.scatter(ts, price, color=color, marker=marker, s=50, alpha=0.7)
            
            self.ax1.set_title("LLM Predictions vs Actual Price Movement")
            self.ax1.set_ylabel("Price")
            self.ax1.grid(True, alpha=0.3)
            self.ax1.legend()
            
            # Calculate and plot accuracy
            if len(self.predictions) >= 10:
                accuracies = self._calculate_rolling_accuracy()
                accuracy_timestamps = timestamps[9:]  # Skip first 9 for rolling window
                
                self.ax2.plot(accuracy_timestamps, accuracies, 'g-', linewidth=2)
                self.ax2.axhline(y=50, color='gray', linestyle='--', alpha=0.7, label='Random (50%)')
                self.ax2.set_ylim(0, 100)
            
            self.ax2.set_title("Prediction Accuracy Over Time (10-period rolling)")
            self.ax2.set_xlabel("Time")
            self.ax2.set_ylabel("Accuracy %")
            self.ax2.grid(True, alpha=0.3)
            self.ax2.legend()
            
            # Format x-axis
            self.figure.autofmt_xdate()
            self.canvas.draw()
            
        except Exception as e:
            print(f"Error updating plots: {str(e)}")
    
    def _calculate_rolling_accuracy(self, window=10) -> List[float]:
        """Calculate rolling accuracy of predictions"""
        accuracies = []
        
        for i in range(window, len(self.predictions)):
            window_predictions = self.predictions[i-window:i]
            correct = 0
            
            for j in range(len(window_predictions) - 1):
                current = window_predictions[j]
                next_pred = window_predictions[j + 1]
                
                current_price = current['current_price']
                next_price = next_pred['current_price']
                predicted_action = current['prediction'].get('action', 'HOLD')
                
                # Check if prediction was correct
                if predicted_action == 'LONG' and next_price > current_price:
                    correct += 1
                elif predicted_action == 'SHORT' and next_price < current_price:
                    correct += 1
                elif predicted_action == 'HOLD' and abs(next_price - current_price) / current_price < 0.001:
                    correct += 1
            
            accuracy = (correct / (len(window_predictions) - 1)) * 100
            accuracies.append(accuracy)
        
        return accuracies


class LLMSimTab(QWidget):
    """Live LLM simulation tab widget"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        self.sim_worker = None
        self.predictions = []
        
        self.init_ui()
    
    def init_ui(self):
        """Initialize the LLM simulation tab UI"""
        layout = QVBoxLayout(self)
        
        # Configuration section
        config_group = QGroupBox("Simulation Configuration")
        config_layout = QGridLayout(config_group)
        
        # Symbol selection
        config_layout.addWidget(QLabel("Symbol:"), 0, 0)
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems(['DOGE/USDT:USDT', 'BTC/USDT:USDT', 'ETH/USDT:USDT'])
        config_layout.addWidget(self.symbol_combo, 0, 1)
        
        # Prediction interval
        config_layout.addWidget(QLabel("Prediction Interval:"), 1, 0)
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(10, 300)
        self.interval_spinbox.setValue(30)
        self.interval_spinbox.setSuffix(" seconds")
        config_layout.addWidget(self.interval_spinbox, 1, 1)
        
        # Enable logging
        self.enable_logging = QCheckBox("Enable Prediction Logging")
        self.enable_logging.setChecked(True)
        config_layout.addWidget(self.enable_logging, 2, 0, 1, 2)
        
        layout.addWidget(config_group)
        
        # Control buttons
        control_layout = QHBoxLayout()
        
        self.start_sim_btn = QPushButton("🚀 Start LLM Simulation")
        self.start_sim_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.start_sim_btn.clicked.connect(self.start_simulation)
        control_layout.addWidget(self.start_sim_btn)
        
        self.stop_sim_btn = QPushButton("⏹️ Stop Simulation")
        self.stop_sim_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        self.stop_sim_btn.clicked.connect(self.stop_simulation)
        self.stop_sim_btn.setEnabled(False)
        control_layout.addWidget(self.stop_sim_btn)
        
        self.clear_data_btn = QPushButton("🗑️ Clear Data")
        self.clear_data_btn.clicked.connect(self.clear_data)
        control_layout.addWidget(self.clear_data_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # Status display
        self.status_label = QLabel("Status: Stopped")
        self.status_label.setStyleSheet("font-weight: bold; padding: 5px;")
        layout.addWidget(self.status_label)
        
        # Create tabs for different views
        tab_widget = QTabWidget()
        
        # Accuracy analysis tab
        self.accuracy_widget = PredictionAccuracyWidget()
        tab_widget.addTab(self.accuracy_widget, "📈 Accuracy Analysis")
        
        # Predictions table tab
        self.predictions_table = QTableWidget()
        tab_widget.addTab(self.predictions_table, "📋 Predictions Log")
        
        # Statistics tab
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)
        
        self.stats_group = QGroupBox("Simulation Statistics")
        self.stats_layout = QGridLayout(self.stats_group)
        stats_layout.addWidget(self.stats_group)
        stats_layout.addStretch()
        
        tab_widget.addTab(stats_widget, "📊 Statistics")
        
        layout.addWidget(tab_widget)
        
        self.update_statistics()
    
    def start_simulation(self):
        """Start LLM simulation"""
        try:
            # Get configuration
            symbol = self.symbol_combo.currentText()
            interval = self.interval_spinbox.value()
            
            # Load system configuration
            from utils.config_validator import ConfigValidator
            config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
            
            # Update UI
            self.start_sim_btn.setEnabled(False)
            self.stop_sim_btn.setEnabled(True)
            self.status_label.setText("Status: Starting...")
            self.status_label.setStyleSheet("color: orange; font-weight: bold; padding: 5px;")
            
            # Start simulation worker
            self.sim_worker = LLMSimulationWorker(symbol, interval, config)
            self.sim_worker.prediction_made.connect(self.on_prediction_made)
            self.sim_worker.error.connect(self.on_simulation_error)
            self.sim_worker.status_update.connect(self.on_status_update)
            self.sim_worker.start()
            
        except Exception as e:
            self.logger.error(f"Error starting simulation: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start simulation: {str(e)}")
            self.reset_simulation_controls()
    
    def stop_simulation(self):
        """Stop LLM simulation"""
        if self.sim_worker:
            self.sim_worker.stop()
            self.sim_worker.wait(5000)
        self.reset_simulation_controls()
    
    def reset_simulation_controls(self):
        """Reset simulation controls"""
        self.start_sim_btn.setEnabled(True)
        self.stop_sim_btn.setEnabled(False)
        self.status_label.setText("Status: Stopped")
        self.status_label.setStyleSheet("color: red; font-weight: bold; padding: 5px;")
    
    def on_prediction_made(self, prediction_data: Dict):
        """Handle new prediction"""
        try:
            self.predictions.append(prediction_data)
            
            # Update accuracy widget
            self.accuracy_widget.add_prediction(prediction_data)
            
            # Update predictions table
            self.update_predictions_table()
            
            # Update statistics
            self.update_statistics()
            
            # Update status
            action = prediction_data['prediction'].get('action', 'UNKNOWN')
            confidence = prediction_data['prediction'].get('confidence', 0)
            self.status_label.setText(f"Status: Running - Last: {action} ({confidence:.1%})")
            self.status_label.setStyleSheet("color: green; font-weight: bold; padding: 5px;")
            
        except Exception as e:
            self.logger.error(f"Error handling prediction: {str(e)}")
    
    def on_simulation_error(self, error_message: str):
        """Handle simulation error"""
        QMessageBox.critical(self, "Simulation Error", f"Simulation failed: {error_message}")
        self.reset_simulation_controls()
    
    def on_status_update(self, status: str):
        """Handle status update"""
        if self.sim_worker and self.sim_worker.isRunning():
            self.status_label.setText(f"Status: {status}")
    
    def update_predictions_table(self):
        """Update predictions table"""
        try:
            self.predictions_table.setRowCount(len(self.predictions))
            self.predictions_table.setColumnCount(6)
            
            headers = ["Time", "Price", "Action", "Confidence", "Reasoning", "Accuracy"]
            self.predictions_table.setHorizontalHeaderLabels(headers)
            
            for i, pred in enumerate(reversed(self.predictions[-50:])):  # Show last 50
                row = len(self.predictions) - 1 - i
                
                # Time
                time_str = datetime.fromtimestamp(pred['timestamp']).strftime("%H:%M:%S")
                self.predictions_table.setItem(row, 0, QTableWidgetItem(time_str))
                
                # Price
                price = f"${pred['current_price']:.6f}"
                self.predictions_table.setItem(row, 1, QTableWidgetItem(price))
                
                # Action
                action = pred['prediction'].get('action', 'UNKNOWN')
                action_item = QTableWidgetItem(action)
                if action == 'LONG':
                    action_item.setBackground(QColor(200, 255, 200))
                elif action == 'SHORT':
                    action_item.setBackground(QColor(255, 200, 200))
                self.predictions_table.setItem(row, 2, action_item)
                
                # Confidence
                confidence = pred['prediction'].get('confidence', 0)
                self.predictions_table.setItem(row, 3, QTableWidgetItem(f"{confidence:.1%}"))
                
                # Reasoning
                reasoning = pred['prediction'].get('reasoning', '')[:50]
                self.predictions_table.setItem(row, 4, QTableWidgetItem(reasoning))
                
                # Accuracy (placeholder for now)
                self.predictions_table.setItem(row, 5, QTableWidgetItem("TBD"))
            
            self.predictions_table.resizeColumnsToContents()
            
        except Exception as e:
            self.logger.error(f"Error updating predictions table: {str(e)}")
    
    def update_statistics(self):
        """Update simulation statistics"""
        try:
            # Clear existing widgets
            for i in reversed(range(self.stats_layout.count())):
                self.stats_layout.itemAt(i).widget().setParent(None)
            
            if not self.predictions:
                label = QLabel("No predictions yet")
                label.setAlignment(Qt.AlignCenter)
                self.stats_layout.addWidget(label, 0, 0, 1, 2)
                return
            
            # Calculate statistics
            total_predictions = len(self.predictions)
            actions = [p['prediction'].get('action', 'HOLD') for p in self.predictions]
            
            long_count = actions.count('LONG')
            short_count = actions.count('SHORT')
            hold_count = actions.count('HOLD')
            
            avg_confidence = np.mean([p['prediction'].get('confidence', 0) for p in self.predictions])
            
            # Display statistics
            stats = [
                ("Total Predictions", str(total_predictions)),
                ("LONG Signals", f"{long_count} ({long_count/total_predictions*100:.1f}%)"),
                ("SHORT Signals", f"{short_count} ({short_count/total_predictions*100:.1f}%)"),
                ("HOLD Signals", f"{hold_count} ({hold_count/total_predictions*100:.1f}%)"),
                ("Avg Confidence", f"{avg_confidence:.1%}"),
                ("Runtime", self._get_runtime())
            ]
            
            for i, (label, value) in enumerate(stats):
                row = i // 2
                col = (i % 2) * 2
                
                label_widget = QLabel(f"{label}:")
                label_widget.setFont(QFont("Arial", 9, QFont.Bold))
                self.stats_layout.addWidget(label_widget, row, col)
                
                value_widget = QLabel(value)
                value_widget.setFont(QFont("Arial", 9))
                self.stats_layout.addWidget(value_widget, row, col + 1)
            
        except Exception as e:
            self.logger.error(f"Error updating statistics: {str(e)}")
    
    def _get_runtime(self) -> str:
        """Get simulation runtime"""
        if not self.predictions:
            return "0 minutes"
        
        start_time = self.predictions[0]['timestamp']
        current_time = datetime.now().timestamp()
        runtime_minutes = (current_time - start_time) / 60
        
        if runtime_minutes < 60:
            return f"{runtime_minutes:.1f} minutes"
        else:
            hours = runtime_minutes / 60
            return f"{hours:.1f} hours"
    
    def clear_data(self):
        """Clear all simulation data"""
        reply = QMessageBox.question(
            self, "Clear Data", "Are you sure you want to clear all simulation data?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.predictions = []
            self.accuracy_widget.predictions = []
            self.accuracy_widget.update_plots()
            self.update_predictions_table()
            self.update_statistics()
    
    def refresh_data(self):
        """Refresh LLM simulation data"""
        self.update_predictions_table()
        self.update_statistics()
