# Epinnox v7 Configuration
# Main configuration file for the LLM-powered crypto futures scalping system

# Database Configuration
database:
  type: sqlite  # sqlite or postgresql
  path: data/epinnox.db  # For SQLite
  echo: false  # Set to true for SQL query logging

  # PostgreSQL settings (when type: postgresql)
  # host: localhost
  # port: 5432
  # database: epinnox
  # username: epinnox
  # password: your_password
  # pool_size: 10
  # max_overflow: 20

  # Backup settings
  backup_enabled: true
  backup_interval_hours: 24
  backup_retention_days: 30

  # Data retention
  data_retention_days: 90  # Keep data for 90 days

# Trading Configuration
symbols: ["BTC/USDT:USDT"]  # Start with just BTC
leverage: 3  # Reduced from 20x to 3x for safety
position_size_pct: 0.02  # Reduced from 5% to 2% per trade
min_spread_pct: 0.002
min_sl_distance_pct: 0.005  # Increased for safer stop losses
cooldown_seconds: 120  # Increased cooldown between trades

# LLM settings
llm:
  api_url: "http://localhost:1234/v1/completions"
  model_name: "Phi-3.1-mini-128k-instruct.Q4_K_M.gguf"  # Updated to match our tested model
  max_tokens: 512
  temperature: 0.3
  timeout: 30
  retry_attempts: 3
  retry_delay: 1


# Legacy settings for backward compatibility
llm_model: "lmstudio-community/meta-llama-3-8b-instruct"
prompt_confidence_threshold: 0.65

# Risk management
risk_limits:
  max_drawdown_pct: 5  # Reduced from 10% to 5%
  min_liquidation_buffer_pct: 5  # Increased from 3% to 5%
  min_win_rate: 0.40  # Increased minimum win rate requirement
  max_margin_usage_pct: 70  # New: Maximum margin usage threshold
  max_total_risk_pct: 80  # New: Maximum total account risk
  position_limits:
    max_positions: 3  # Maximum concurrent positions
    max_size_per_position_pct: 2  # Maximum size per position as % of account
    risk_based_sizing: true  # Enable dynamic sizing based on risk levels

# Performance tracking
performance_window: 10

# Debug and operational modes
debug_mode: true
live_mode: false  # Start in paper trading mode first
log_level: "INFO"

# Logging configuration
logging:
  heartbeat_interval: 30
  max_log_size: 100
  backup_count: 5
  log_dir: "logs"
  
  debug_logs:
    prompts: "logs/llm_prompts.log"
    responses: "logs/llm_responses.log"
    rejected_signals: "logs/rejected_signals.jsonl"
    heartbeat: "logs/heartbeat.log"

# Exchange configuration
exchange:
  name: "huobi"
  testnet: false
  # API credentials should be loaded from environment variables
  api_key: "${HTX_API_KEY}"
  api_secret: "${HTX_API_SECRET}"
  
  market_type: "swap"
  position_mode: "hedge"
  
  max_position_size_usd: 50  # Reduced maximum position size
  min_position_size_usd: 10
  max_leverage: 3        # Match the global leverage setting
