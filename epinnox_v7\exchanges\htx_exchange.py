"""
HTX (Huobi) Exchange Implementation for Epinnox v7

Production-ready HTX exchange integration with live trading capabilities.
"""

import asyncio
import aiohttp
import hmac
import hashlib
import base64
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from urllib.parse import urlencode

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from exchanges.base_exchange import (
    BaseExchange, MarketData, Position, Order, Balance,
    OrderType, OrderSide, OrderStatus
)
from utils.logger import get_logger

logger = get_logger()


class HTXExchange(BaseExchange):
    """HTX (Huobi) exchange implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # HTX API endpoints
        self.base_url = "https://api.huobi.pro"
        self.ws_url = "wss://api.huobi.pro/ws"
        
        # Session for HTTP requests
        self.session = None
        
        # WebSocket connection
        self.ws = None
        
        # Symbol mapping
        self.symbol_map = {}
        
        self.logger.info("HTX Exchange initialized")
    
    async def connect(self) -> bool:
        """Connect to HTX API"""
        try:
            self.session = aiohttp.ClientSession()
            
            # Test connection with public endpoint
            async with self.session.get(f"{self.base_url}/v1/common/timestamp") as response:
                if response.status == 200:
                    self.connected = True
                    self.logger.info("Connected to HTX API")
                    
                    # Load trading symbols
                    await self._load_symbols()
                    
                    return True
                else:
                    self.logger.error(f"Failed to connect to HTX API: {response.status}")
                    return False
        
        except Exception as e:
            self.logger.error(f"Error connecting to HTX: {str(e)}")
            return False
    
    async def disconnect(self):
        """Disconnect from HTX API"""
        if self.ws:
            await self.ws.close()
            self.ws = None
        
        if self.session:
            await self.session.close()
            self.session = None
        
        self.connected = False
        self.authenticated = False
        self.logger.info("Disconnected from HTX API")
    
    async def authenticate(self, api_key: str, api_secret: str, passphrase: str = None) -> bool:
        """Authenticate with HTX API"""
        self.api_key = api_key
        self.api_secret = api_secret
        
        try:
            # Test authentication with account info
            account_info = await self._get_account_info()
            if account_info and len(account_info) > 0:
                self.authenticated = True
                self.logger.info(f"Successfully authenticated with HTX - {len(account_info)} accounts found")
                return True
            else:
                self.logger.error("HTX authentication failed - no accounts found")
                return False
        
        except Exception as e:
            self.logger.error(f"HTX authentication error: {str(e)}")
            return False
    
    async def get_market_data(self, symbol: str) -> MarketData:
        """Get current market data for symbol"""
        htx_symbol = self._format_symbol(symbol)
        
        try:
            # Get ticker data
            async with self.session.get(f"{self.base_url}/market/detail/merged", 
                                      params={'symbol': htx_symbol}) as response:
                data = await response.json()
                
                if data['status'] == 'ok':
                    tick = data['tick']
                    
                    return MarketData(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        bid=float(tick['bid'][0]),
                        ask=float(tick['ask'][0]),
                        last=float(tick['close']),
                        volume=float(tick['vol']),
                        high_24h=float(tick['high']),
                        low_24h=float(tick['low']),
                        change_24h=float(tick['close']) - float(tick['open']),
                        change_pct_24h=((float(tick['close']) - float(tick['open'])) / float(tick['open'])) * 100
                    )
                else:
                    raise Exception(f"HTX API error: {data}")
        
        except Exception as e:
            self.logger.error(f"Error getting market data for {symbol}: {str(e)}")
            raise
    
    async def get_orderbook(self, symbol: str, limit: int = 20) -> Dict[str, List[List[float]]]:
        """Get order book for symbol"""
        htx_symbol = self._format_symbol(symbol)
        
        try:
            async with self.session.get(f"{self.base_url}/market/depth", 
                                      params={'symbol': htx_symbol, 'depth': limit}) as response:
                data = await response.json()
                
                if data['status'] == 'ok':
                    tick = data['tick']
                    return {
                        'bids': tick['bids'],
                        'asks': tick['asks'],
                        'timestamp': tick['ts']
                    }
                else:
                    raise Exception(f"HTX API error: {data}")
        
        except Exception as e:
            self.logger.error(f"Error getting orderbook for {symbol}: {str(e)}")
            raise
    
    async def get_klines(self, symbol: str, interval: str, limit: int = 100) -> List[List[float]]:
        """Get candlestick data"""
        htx_symbol = self._format_symbol(symbol)
        htx_interval = self._convert_interval(interval)
        
        try:
            async with self.session.get(f"{self.base_url}/market/history/kline", 
                                      params={
                                          'symbol': htx_symbol,
                                          'period': htx_interval,
                                          'size': limit
                                      }) as response:
                data = await response.json()
                
                if data['status'] == 'ok':
                    return [[
                        kline['id'] * 1000,  # timestamp
                        kline['open'],       # open
                        kline['high'],       # high
                        kline['low'],        # low
                        kline['close'],      # close
                        kline['vol']         # volume
                    ] for kline in data['data']]
                else:
                    raise Exception(f"HTX API error: {data}")
        
        except Exception as e:
            self.logger.error(f"Error getting klines for {symbol}: {str(e)}")
            raise
    
    async def get_account_balance(self) -> List[Balance]:
        """Get account balances - includes both spot and futures"""
        try:
            balances = []

            # Get spot balances
            spot_balances = await self._get_spot_balances()
            balances.extend(spot_balances)

            # Get futures balances
            futures_balances = await self._get_futures_balances()
            balances.extend(futures_balances)

            return balances

        except Exception as e:
            self.logger.error(f"Error getting account balance: {str(e)}")
            raise

    async def _get_spot_balances(self) -> List[Balance]:
        """Get spot account balances"""
        try:
            account_info = await self._get_account_info()
            if not account_info:
                return []

            account_id = account_info[0]['id']

            # Get account balance
            endpoint = f"/v1/account/accounts/{account_id}/balance"
            params = await self._sign_request('GET', endpoint)

            async with self.session.get(f"{self.base_url}{endpoint}", params=params) as response:
                data = await response.json()

                if data['status'] == 'ok':
                    balances = []
                    balance_dict = {}

                    # Group by currency
                    for item in data['data']['list']:
                        currency = item['currency'].upper()
                        balance_type = item['type']
                        balance = float(item['balance'])

                        if currency not in balance_dict:
                            balance_dict[currency] = {'free': 0.0, 'used': 0.0}

                        if balance_type == 'trade':
                            balance_dict[currency]['free'] = balance
                        elif balance_type == 'frozen':
                            balance_dict[currency]['used'] = balance

                    # Convert to Balance objects
                    for currency, bal in balance_dict.items():
                        if bal['free'] > 0 or bal['used'] > 0:
                            balances.append(Balance(
                                currency=f"{currency}_SPOT",
                                free=bal['free'],
                                used=bal['used'],
                                total=bal['free'] + bal['used']
                            ))

                    return balances
                else:
                    self.logger.warning(f"HTX spot balance API error: {data}")
                    return []

        except Exception as e:
            self.logger.warning(f"Error getting spot balance: {str(e)}")
            return []

    async def _get_futures_balances(self) -> List[Balance]:
        """Get futures account balances"""
        try:
            # HTX Futures API endpoint
            futures_url = "https://api.hbdm.com"
            endpoint = "/linear-swap-api/v1/swap_account_info"

            # Create signature for futures API
            timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')

            params = {
                'AccessKeyId': self.api_key,
                'SignatureMethod': 'HmacSHA256',
                'SignatureVersion': '2',
                'Timestamp': timestamp
            }

            # Sort and encode parameters
            sorted_params = sorted(params.items())
            query_string = urlencode(sorted_params)

            # Create signature payload
            payload = f"POST\napi.hbdm.com\n{endpoint}\n{query_string}"

            # Generate signature
            signature = base64.b64encode(
                hmac.new(
                    self.api_secret.encode('utf-8'),
                    payload.encode('utf-8'),
                    hashlib.sha256
                ).digest()
            ).decode('utf-8')

            params['Signature'] = signature

            async with self.session.post(f"{futures_url}{endpoint}",
                                        params=params,
                                        headers={'Content-Type': 'application/json'}) as response:
                data = await response.json()

                if data.get('status') == 'ok' and 'data' in data:
                    balances = []
                    for account in data['data']:
                        currency = account['symbol'].upper()
                        margin_balance = float(account.get('margin_balance', 0))
                        margin_frozen = float(account.get('margin_frozen', 0))

                        if margin_balance > 0 or margin_frozen > 0:
                            balances.append(Balance(
                                currency=f"{currency}_FUTURES",
                                free=margin_balance - margin_frozen,
                                used=margin_frozen,
                                total=margin_balance
                            ))

                    return balances
                else:
                    self.logger.warning(f"HTX futures balance API response: {data}")
                    return []

        except Exception as e:
            self.logger.warning(f"Error getting futures balance: {str(e)}")
            return []
    
    async def get_positions(self) -> List[Position]:
        """Get open futures positions"""
        try:
            # HTX Futures API endpoint for positions
            futures_url = "https://api.hbdm.com"
            endpoint = "/linear-swap-api/v1/swap_position_info"

            # Create signature for futures API
            timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')

            params = {
                'AccessKeyId': self.api_key,
                'SignatureMethod': 'HmacSHA256',
                'SignatureVersion': '2',
                'Timestamp': timestamp
            }

            # Sort and encode parameters
            sorted_params = sorted(params.items())
            query_string = urlencode(sorted_params)

            # Create signature payload
            payload = f"POST\napi.hbdm.com\n{endpoint}\n{query_string}"

            # Generate signature
            signature = base64.b64encode(
                hmac.new(
                    self.api_secret.encode('utf-8'),
                    payload.encode('utf-8'),
                    hashlib.sha256
                ).digest()
            ).decode('utf-8')

            params['Signature'] = signature

            async with self.session.post(f"{futures_url}{endpoint}",
                                        params=params,
                                        headers={'Content-Type': 'application/json'}) as response:
                data = await response.json()

                if data.get('status') == 'ok' and 'data' in data:
                    positions = []
                    for pos_data in data['data']:
                        if float(pos_data.get('volume', 0)) > 0:  # Only active positions
                            positions.append(Position(
                                symbol=pos_data['contract_code'],
                                side='long' if pos_data['direction'] == 'buy' else 'short',
                                size=float(pos_data['volume']),
                                entry_price=float(pos_data['cost_open']),
                                mark_price=float(pos_data.get('last_price', 0)),
                                pnl=float(pos_data.get('profit_unreal', 0)),
                                margin=float(pos_data.get('position_margin', 0)),
                                timestamp=datetime.now()
                            ))

                    return positions
                else:
                    self.logger.warning(f"HTX futures positions API response: {data}")
                    return []

        except Exception as e:
            self.logger.warning(f"Error getting futures positions: {str(e)}")
            return []
    
    async def get_open_orders(self, symbol: str = None) -> List[Order]:
        """Get open orders"""
        try:
            endpoint = "/v1/order/openOrders"
            params = {}
            
            if symbol:
                params['symbol'] = self._format_symbol(symbol)
            
            signed_params = await self._sign_request('GET', endpoint, params)
            
            async with self.session.get(f"{self.base_url}{endpoint}", params=signed_params) as response:
                data = await response.json()
                
                if data['status'] == 'ok':
                    orders = []
                    for order_data in data['data']:
                        orders.append(Order(
                            id=str(order_data['id']),
                            symbol=self._parse_symbol(order_data['symbol']),
                            type=OrderType.LIMIT if order_data['type'].endswith('limit') else OrderType.MARKET,
                            side=OrderSide.BUY if order_data['type'].startswith('buy') else OrderSide.SELL,
                            amount=float(order_data['amount']),
                            price=float(order_data['price']) if order_data['price'] else None,
                            status=self._convert_order_status(order_data['state']),
                            filled=float(order_data['filled-amount']),
                            remaining=float(order_data['amount']) - float(order_data['filled-amount']),
                            timestamp=datetime.fromtimestamp(order_data['created-at'] / 1000),
                            fee=float(order_data.get('filled-fees', 0))
                        ))
                    
                    return orders
                else:
                    raise Exception(f"HTX API error: {data}")
        
        except Exception as e:
            self.logger.error(f"Error getting open orders: {str(e)}")
            raise
    
    async def place_order(self, symbol: str, side: OrderSide, type: OrderType, 
                         amount: float, price: float = None, 
                         stop_price: float = None, **kwargs) -> Order:
        """Place an order"""
        # Validate parameters
        valid, error_msg = self.validate_order_params(symbol, side, type, amount, price)
        if not valid:
            raise ValueError(error_msg)
        
        try:
            account_info = await self._get_account_info()
            account_id = account_info[0]['id']
            
            # Prepare order data
            order_data = {
                'account-id': account_id,
                'symbol': self._format_symbol(symbol),
                'type': f"{side.value}-{type.value}",
                'amount': str(amount)
            }
            
            if price is not None:
                order_data['price'] = str(price)
            
            endpoint = "/v1/order/orders/place"
            signed_params = await self._sign_request('POST', endpoint, order_data)
            
            async with self.session.post(f"{self.base_url}{endpoint}", 
                                       json=order_data, 
                                       params=signed_params) as response:
                data = await response.json()
                
                if data['status'] == 'ok':
                    order_id = data['data']
                    
                    # Get order details
                    order_details = await self._get_order_details(order_id)
                    
                    return Order(
                        id=str(order_id),
                        symbol=symbol,
                        type=type,
                        side=side,
                        amount=amount,
                        price=price,
                        status=OrderStatus.PENDING,
                        filled=0.0,
                        remaining=amount,
                        timestamp=datetime.now()
                    )
                else:
                    raise Exception(f"HTX API error: {data}")
        
        except Exception as e:
            self.logger.error(f"Error placing order: {str(e)}")
            raise
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        try:
            endpoint = f"/v1/order/orders/{order_id}/submitcancel"
            signed_params = await self._sign_request('POST', endpoint)
            
            async with self.session.post(f"{self.base_url}{endpoint}", 
                                       params=signed_params) as response:
                data = await response.json()
                
                if data['status'] == 'ok':
                    self.logger.info(f"Order {order_id} cancelled successfully")
                    return True
                else:
                    self.logger.error(f"Failed to cancel order {order_id}: {data}")
                    return False
        
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {str(e)}")
            return False
    
    async def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all orders"""
        try:
            open_orders = await self.get_open_orders(symbol)
            cancelled_count = 0
            
            for order in open_orders:
                if await self.cancel_order(order.id, order.symbol):
                    cancelled_count += 1
            
            self.logger.info(f"Cancelled {cancelled_count} orders")
            return cancelled_count
        
        except Exception as e:
            self.logger.error(f"Error cancelling all orders: {str(e)}")
            return 0
    
    async def get_trading_fees(self, symbol: str = None) -> Dict[str, float]:
        """Get trading fees"""
        # HTX typically charges 0.2% for both maker and taker
        return {
            'maker': 0.002,
            'taker': 0.002
        }
    
    # Private helper methods
    async def _get_account_info(self) -> List[Dict[str, Any]]:
        """Get account information"""
        endpoint = "/v1/account/accounts"
        method = "GET"

        # Create signature using the same method as our working test
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')

        params = {
            'AccessKeyId': self.api_key,
            'SignatureMethod': 'HmacSHA256',
            'SignatureVersion': '2',
            'Timestamp': timestamp
        }

        # Sort and encode parameters
        sorted_params = sorted(params.items())
        query_string = urlencode(sorted_params)

        # Create signature payload
        payload = f"{method}\napi.huobi.pro\n{endpoint}\n{query_string}"

        # Generate signature
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')

        params['Signature'] = signature

        async with self.session.get(f"{self.base_url}{endpoint}", params=params) as response:
            data = await response.json()

            if data['status'] == 'ok':
                return data['data']
            else:
                raise Exception(f"HTX API error: {data}")
    
    async def _get_order_details(self, order_id: str) -> Dict[str, Any]:
        """Get order details"""
        endpoint = f"/v1/order/orders/{order_id}"
        params = await self._sign_request('GET', endpoint)
        
        async with self.session.get(f"{self.base_url}{endpoint}", params=params) as response:
            data = await response.json()
            
            if data['status'] == 'ok':
                return data['data']
            else:
                raise Exception(f"HTX API error: {data}")
    
    async def _sign_request(self, method: str, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, str]:
        """Sign API request"""
        if not self.api_key or not self.api_secret:
            raise Exception("API credentials not set")

        # Use current UTC time
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')

        # Prepare parameters
        sign_params = {
            'AccessKeyId': self.api_key,
            'SignatureMethod': 'HmacSHA256',
            'SignatureVersion': '2',
            'Timestamp': timestamp
        }

        if params:
            sign_params.update(params)

        # Sort parameters for consistent signature
        sorted_params = sorted(sign_params.items())
        query_string = urlencode(sorted_params)

        # Create payload for signature
        payload = f"{method}\napi.huobi.pro\n{endpoint}\n{query_string}"

        # Create signature
        signature = base64.b64encode(
            hmac.new(
                self.api_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')

        sign_params['Signature'] = signature

        return sign_params
    
    async def _load_symbols(self):
        """Load trading symbols"""
        try:
            async with self.session.get(f"{self.base_url}/v1/common/symbols") as response:
                data = await response.json()
                
                if data['status'] == 'ok':
                    for symbol_info in data['data']:
                        if symbol_info['state'] == 'online':
                            base = symbol_info['base-currency'].upper()
                            quote = symbol_info['quote-currency'].upper()
                            standard_symbol = f"{base}/{quote}"
                            htx_symbol = symbol_info['symbol']
                            
                            self.symbol_map[standard_symbol] = htx_symbol
                            self.symbol_map[htx_symbol] = standard_symbol
        
        except Exception as e:
            self.logger.error(f"Error loading symbols: {str(e)}")
    
    def _format_symbol(self, symbol: str) -> str:
        """Convert standard symbol to HTX format"""
        if symbol in self.symbol_map:
            return self.symbol_map[symbol]
        
        # Fallback: convert BTC/USDT to btcusdt
        if '/' in symbol:
            base, quote = symbol.split('/')
            return f"{base.lower()}{quote.lower()}"
        
        return symbol.lower()
    
    def _parse_symbol(self, htx_symbol: str) -> str:
        """Convert HTX symbol to standard format"""
        if htx_symbol in self.symbol_map:
            return self.symbol_map[htx_symbol]
        
        return htx_symbol.upper()
    
    def _convert_interval(self, interval: str) -> str:
        """Convert standard interval to HTX format"""
        interval_map = {
            '1m': '1min',
            '5m': '5min',
            '15m': '15min',
            '30m': '30min',
            '1h': '60min',
            '4h': '4hour',
            '1d': '1day'
        }
        return interval_map.get(interval, '1min')
    
    def _convert_order_status(self, htx_status: str) -> OrderStatus:
        """Convert HTX order status to standard format"""
        status_map = {
            'submitted': OrderStatus.PENDING,
            'partial-filled': OrderStatus.OPEN,
            'filled': OrderStatus.FILLED,
            'canceled': OrderStatus.CANCELLED,
            'partial-canceled': OrderStatus.CANCELLED
        }
        return status_map.get(htx_status, OrderStatus.PENDING)
