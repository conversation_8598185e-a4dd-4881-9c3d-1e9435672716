"""
Enhanced Production Dashboard for Epinnox v7

Complete production-ready trading interface with real-time monitoring,
position management, risk controls, and comprehensive safety features.
"""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout,
    QWidget, QPushButton, QLabel, QStatusBar, QMenuBar, QAction,
    QMessageBox, QSplashScreen, QProgressBar, QGridLayout, QGroupBox,
    QCheckBox, QComboBox, QDockWidget, QScrollArea, QFrame, QSplitter,
    QToolBar, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSettings, QThread
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.config_validator import ConfigValidator
from utils.logger import get_logger, setup_logger
from auth.login_dialog import show_login_dialog
from auth.authentication import session_manager
from auth.decorators import require_auth, admin_required
from gui.data_manager import get_data_manager
from gui.widgets.trading_controls import TradingControlsWidget
from gui.widgets.position_manager import PositionManagerWidget
from gui.widgets.market_data import MarketDataWidget
from gui.widgets.risk_dashboard import RiskDashboardWidget

logger = get_logger()


class ProductionDashboard(QMainWindow):
    """
    Enhanced production trading dashboard for Epinnox v7

    Features:
    - Real-time trading controls with safety confirmations
    - Live position management with one-click actions
    - Market data feeds with interactive charts
    - Risk monitoring with circuit breaker status
    - System health monitoring and alerts
    - Customizable layout with dockable widgets
    - Dark/light theme support
    - Keyboard shortcuts for common actions
    """

    # Signals
    trading_started = pyqtSignal()
    trading_stopped = pyqtSignal()
    emergency_stop_triggered = pyqtSignal()
    theme_changed = pyqtSignal(str)

    def __init__(self, config: Dict[str, Any] = None):
        super().__init__()
        self.logger = get_logger()
        self.logger.info("Initializing Epinnox v7 Production Dashboard...")

        # Configuration
        if config is None:
            self.config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
        else:
            self.config = config

        # Settings for layout persistence
        self.settings = QSettings("Epinnox", "v7_dashboard")

        # Data manager with exchange manager
        from exchanges.exchange_manager import get_exchange_manager
        exchange_manager = get_exchange_manager()
        self.data_manager = get_data_manager(exchange_manager)

        # Theme
        self.current_theme = self.settings.value("theme", "light")

        # Initialize UI
        self.setup_ui()
        self.setup_widgets()
        self.setup_connections()
        self.setup_shortcuts()
        self.apply_theme(self.current_theme)

        # Start data updates
        self.data_manager.start_updates()

        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second

        # Restore layout
        self.restore_layout()

        self.logger.info("Production Dashboard initialized successfully")

    def setup_ui(self):
        """Setup the main UI structure"""
        self.setWindowTitle("Epinnox v7 - Production Trading Dashboard")
        self.setGeometry(100, 100, 1600, 1000)

        # Set application icon
        try:
            self.setWindowIcon(QIcon("assets/epinnox_icon.png"))
        except:
            pass

        # Create central widget with main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout - use splitter for resizable sections
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Create header
        self.header_widget = self.create_header()
        main_layout.addWidget(self.header_widget)

        # Create main content area with splitters
        self.main_splitter = QSplitter(Qt.Horizontal)

        # Left panel - Trading controls and risk
        self.left_panel = self.create_left_panel()
        self.main_splitter.addWidget(self.left_panel)

        # Center panel - Market data and charts
        self.center_panel = self.create_center_panel()
        self.main_splitter.addWidget(self.center_panel)

        # Right panel - Positions and alerts
        self.right_panel = self.create_right_panel()
        self.main_splitter.addWidget(self.right_panel)

        # Set splitter proportions
        self.main_splitter.setSizes([400, 800, 400])

        main_layout.addWidget(self.main_splitter)

        # Create status bar
        self.setup_status_bar()

        # Create menu bar
        self.setup_menu_bar()

        # Create toolbar
        self.setup_toolbar()

    def create_header(self) -> QWidget:
        """Create dashboard header with title and user info"""
        header = QFrame()
        header.setFrameStyle(QFrame.StyledPanel)
        header.setMaximumHeight(60)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(10, 5, 10, 5)

        # Title and logo
        title_layout = QHBoxLayout()

        # Logo (if available)
        try:
            logo_label = QLabel()
            logo_pixmap = QPixmap("assets/epinnox_logo_small.png")
            logo_label.setPixmap(logo_pixmap.scaled(40, 40, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            title_layout.addWidget(logo_label)
        except:
            pass

        title_label = QLabel("Epinnox v7 Production Dashboard")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_layout.addWidget(title_label)

        layout.addLayout(title_layout)

        layout.addStretch()

        # System status indicator
        self.system_status_widget = self.create_system_status_widget()
        layout.addWidget(self.system_status_widget)

        # User info
        if session_manager.is_authenticated():
            user_info = self.create_user_info_widget()
            layout.addWidget(user_info)

        return header

    def create_system_status_widget(self) -> QWidget:
        """Create system status indicator widget"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(10, 5, 10, 5)

        # Connection status
        self.connection_status = QLabel("🔴 Disconnected")
        self.connection_status.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(self.connection_status)

        # Trading status
        self.trading_status = QLabel("⏹️ Stopped")
        self.trading_status.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(self.trading_status)

        # Mode indicator
        self.mode_indicator = QLabel("📊 Dry Run")
        self.mode_indicator.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(self.mode_indicator)

        return widget

    def create_user_info_widget(self) -> QWidget:
        """Create user information widget"""
        widget = QFrame()
        widget.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(10, 5, 10, 5)

        # User details
        user = session_manager.current_user
        user_label = QLabel(f"👤 {user.username}")
        user_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(user_label)

        role_label = QLabel(f"({user.role.value})")
        role_label.setStyleSheet("color: #666;")
        layout.addWidget(role_label)

        # Logout button
        logout_btn = QPushButton("Logout")
        logout_btn.setMaximumWidth(80)
        logout_btn.clicked.connect(self.logout)
        layout.addWidget(logout_btn)

        return widget

    def create_left_panel(self) -> QWidget:
        """Create left panel with trading controls and risk dashboard"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)

        # Trading controls
        self.trading_controls = TradingControlsWidget()
        layout.addWidget(self.trading_controls)

        # Risk dashboard
        self.risk_dashboard = RiskDashboardWidget()
        layout.addWidget(self.risk_dashboard)

        return panel

    def create_center_panel(self) -> QWidget:
        """Create center panel with market data and charts"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)

        # Market data widget
        self.market_data = MarketDataWidget()
        layout.addWidget(self.market_data)

        return panel

    def create_right_panel(self) -> QWidget:
        """Create right panel with positions and system monitoring"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)

        # Position manager
        self.position_manager = PositionManagerWidget()
        layout.addWidget(self.position_manager)

        # System monitor (placeholder for now)
        system_monitor = QGroupBox("System Monitor")
        monitor_layout = QVBoxLayout(system_monitor)

        # CPU usage
        cpu_layout = QHBoxLayout()
        cpu_layout.addWidget(QLabel("CPU:"))
        self.cpu_bar = QProgressBar()
        self.cpu_bar.setRange(0, 100)
        self.cpu_bar.setValue(25)
        cpu_layout.addWidget(self.cpu_bar)
        monitor_layout.addLayout(cpu_layout)

        # Memory usage
        mem_layout = QHBoxLayout()
        mem_layout.addWidget(QLabel("Memory:"))
        self.memory_bar = QProgressBar()
        self.memory_bar.setRange(0, 100)
        self.memory_bar.setValue(45)
        mem_layout.addWidget(self.memory_bar)
        monitor_layout.addLayout(mem_layout)

        # Network latency
        net_layout = QHBoxLayout()
        net_layout.addWidget(QLabel("Latency:"))
        self.latency_label = QLabel("12ms")
        self.latency_label.setStyleSheet("font-weight: bold; color: green;")
        net_layout.addWidget(self.latency_label)
        net_layout.addStretch()
        monitor_layout.addLayout(net_layout)

        layout.addWidget(system_monitor)

        return panel

    def setup_widgets(self):
        """Setup widget-specific configurations"""
        # Configure trading controls
        if hasattr(self, 'trading_controls'):
            pass  # Already configured in widget

        # Configure position manager
        if hasattr(self, 'position_manager'):
            pass  # Already configured in widget

        # Configure market data
        if hasattr(self, 'market_data'):
            pass  # Already configured in widget

        # Configure risk dashboard
        if hasattr(self, 'risk_dashboard'):
            pass  # Already configured in widget

    def setup_connections(self):
        """Setup signal connections between widgets"""
        # Trading controls signals
        if hasattr(self, 'trading_controls'):
            self.trading_controls.trading_started.connect(self.on_trading_started)
            self.trading_controls.trading_stopped.connect(self.on_trading_stopped)
            self.trading_controls.emergency_stop_triggered.connect(self.on_emergency_stop)
            self.trading_controls.manual_trade_requested.connect(self.on_manual_trade)

        # Position manager signals
        if hasattr(self, 'position_manager'):
            self.position_manager.position_closed.connect(self.on_position_closed)
            self.position_manager.position_modified.connect(self.on_position_modified)

        # Market data signals
        if hasattr(self, 'market_data'):
            self.market_data.symbol_selected.connect(self.on_symbol_selected)

        # Risk dashboard signals
        if hasattr(self, 'risk_dashboard'):
            self.risk_dashboard.risk_limit_breached.connect(self.on_risk_limit_breached)
            self.risk_dashboard.circuit_breaker_triggered.connect(self.on_circuit_breaker)

        # Data manager signals
        self.data_manager.market_data_updated.connect(self.on_market_data_updated)
        self.data_manager.account_updated.connect(self.on_account_updated)
        self.data_manager.system_status_updated.connect(self.on_system_status_updated)
        self.data_manager.alert_triggered.connect(self.on_alert_triggered)

    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence

        # F5 - Refresh data
        refresh_shortcut = QShortcut(QKeySequence("F5"), self)
        refresh_shortcut.activated.connect(self.refresh_all_data)

        # Ctrl+T - Toggle theme
        theme_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        theme_shortcut.activated.connect(self.toggle_theme)

        # Ctrl+Q - Quit
        quit_shortcut = QShortcut(QKeySequence("Ctrl+Q"), self)
        quit_shortcut.activated.connect(self.close)

        # Space - Start/Stop trading
        space_shortcut = QShortcut(QKeySequence("Space"), self)
        space_shortcut.activated.connect(self.toggle_trading)

        # Esc - Emergency stop (admin only)
        if session_manager.is_authenticated():
            from auth.models import UserRole
            if session_manager.has_role(UserRole.ADMIN):
                esc_shortcut = QShortcut(QKeySequence("Escape"), self)
                esc_shortcut.activated.connect(self.emergency_stop_shortcut)

    def setup_status_bar(self):
        """Setup status bar with indicators"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Connection indicator
        self.status_connection = QLabel("🔴 Disconnected")
        self.status_bar.addWidget(self.status_connection)

        # Trading mode
        self.status_mode = QLabel("📊 Dry Run")
        self.status_bar.addWidget(self.status_mode)

        # Account balance
        self.status_balance = QLabel("Balance: $0.00")
        self.status_bar.addWidget(self.status_balance)

        # Daily P&L
        self.status_pnl = QLabel("Daily P&L: $0.00")
        self.status_bar.addWidget(self.status_pnl)

        # Last update
        self.status_update = QLabel("Last Update: Never")
        self.status_bar.addPermanentWidget(self.status_update)

        # Version
        version_label = QLabel("Epinnox v7.0")
        self.status_bar.addPermanentWidget(version_label)

    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu('&File')

        # New strategy action
        new_action = QAction('&New Strategy...', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_strategy)
        file_menu.addAction(new_action)

        # Open strategy action
        open_action = QAction('&Open Strategy...', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_strategy)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # Export data action
        export_action = QAction('&Export Data...', self)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction('E&xit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Trading menu
        trading_menu = menubar.addMenu('&Trading')

        # Start trading
        start_action = QAction('&Start Trading', self)
        start_action.setShortcut('F1')
        start_action.triggered.connect(self.start_trading)
        trading_menu.addAction(start_action)

        # Stop trading
        stop_action = QAction('S&top Trading', self)
        stop_action.setShortcut('F2')
        stop_action.triggered.connect(self.stop_trading)
        trading_menu.addAction(stop_action)

        trading_menu.addSeparator()

        # Emergency stop (admin only)
        if session_manager.is_authenticated():
            from auth.models import UserRole
            if session_manager.has_role(UserRole.ADMIN):
                emergency_action = QAction('🛑 &Emergency Stop', self)
                emergency_action.setShortcut('Ctrl+E')
                emergency_action.triggered.connect(self.emergency_stop)
                trading_menu.addAction(emergency_action)

        # View menu
        view_menu = menubar.addMenu('&View')

        # Theme submenu
        theme_menu = view_menu.addMenu('&Theme')

        light_action = QAction('&Light', self)
        light_action.triggered.connect(lambda: self.apply_theme('light'))
        theme_menu.addAction(light_action)

        dark_action = QAction('&Dark', self)
        dark_action.triggered.connect(lambda: self.apply_theme('dark'))
        theme_menu.addAction(dark_action)

        view_menu.addSeparator()

        # Refresh action
        refresh_action = QAction('&Refresh All', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_all_data)
        view_menu.addAction(refresh_action)

        # Tools menu
        tools_menu = menubar.addMenu('&Tools')

        # Settings action
        settings_action = QAction('&Settings...', self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # Risk settings (admin only)
        if session_manager.is_authenticated():
            from auth.models import UserRole
            if session_manager.has_role(UserRole.ADMIN):
                risk_action = QAction('&Risk Settings...', self)
                risk_action.triggered.connect(self.show_risk_settings)
                tools_menu.addAction(risk_action)

        # Help menu
        help_menu = menubar.addMenu('&Help')

        # Documentation action
        docs_action = QAction('&Documentation', self)
        docs_action.setShortcut('F1')
        docs_action.triggered.connect(self.show_documentation)
        help_menu.addAction(docs_action)

        # About action
        about_action = QAction('&About Epinnox v7', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """Setup toolbar with quick actions"""
        toolbar = QToolBar("Main Toolbar")
        self.addToolBar(toolbar)

        # Start/Stop trading
        self.start_action = QAction("▶️ Start", self)
        self.start_action.setToolTip("Start Trading (Space)")
        self.start_action.triggered.connect(self.start_trading)
        toolbar.addAction(self.start_action)

        self.stop_action = QAction("⏹️ Stop", self)
        self.stop_action.setToolTip("Stop Trading (F2)")
        self.stop_action.triggered.connect(self.stop_trading)
        self.stop_action.setEnabled(False)
        toolbar.addAction(self.stop_action)

        toolbar.addSeparator()

        # Emergency stop (admin only)
        if session_manager.is_authenticated():
            from auth.models import UserRole
            if session_manager.has_role(UserRole.ADMIN):
                emergency_action = QAction("🛑 EMERGENCY", self)
                emergency_action.setToolTip("Emergency Stop (Ctrl+E)")
                emergency_action.triggered.connect(self.emergency_stop)
                toolbar.addAction(emergency_action)
                toolbar.addSeparator()

        # Refresh
        refresh_action = QAction("🔄 Refresh", self)
        refresh_action.setToolTip("Refresh All Data (F5)")
        refresh_action.triggered.connect(self.refresh_all_data)
        toolbar.addAction(refresh_action)

        # Theme toggle
        theme_action = QAction("🌓 Theme", self)
        theme_action.setToolTip("Toggle Theme (Ctrl+T)")
        theme_action.triggered.connect(self.toggle_theme)
        toolbar.addAction(theme_action)

    # Event Handlers
    def on_trading_started(self):
        """Handle trading started event"""
        self.trading_status.setText("▶️ Running")
        self.trading_status.setStyleSheet("color: green; font-weight: bold;")

        self.start_action.setEnabled(False)
        self.stop_action.setEnabled(True)

        self.status_bar.showMessage("Trading started", 3000)
        self.logger.info("Trading started from dashboard")

    def on_trading_stopped(self):
        """Handle trading stopped event"""
        self.trading_status.setText("⏹️ Stopped")
        self.trading_status.setStyleSheet("color: red; font-weight: bold;")

        self.start_action.setEnabled(True)
        self.stop_action.setEnabled(False)

        self.status_bar.showMessage("Trading stopped", 3000)
        self.logger.info("Trading stopped from dashboard")

    def on_emergency_stop(self):
        """Handle emergency stop event"""
        self.trading_status.setText("🛑 EMERGENCY")
        self.trading_status.setStyleSheet("color: white; background-color: red; font-weight: bold; padding: 2px;")

        self.start_action.setEnabled(False)
        self.stop_action.setEnabled(False)

        self.status_bar.showMessage("EMERGENCY STOP ACTIVATED", 10000)
        self.logger.critical("Emergency stop activated from dashboard")

        # Show emergency notification
        QMessageBox.critical(
            self,
            "EMERGENCY STOP",
            "🛑 EMERGENCY STOP ACTIVATED\n\nAll trading has been halted immediately."
        )

    def on_manual_trade(self, trade_data: Dict[str, Any]):
        """Handle manual trade request"""
        self.logger.info(f"Manual trade requested: {trade_data}")
        # TODO: Implement actual trade execution
        self.status_bar.showMessage(f"Manual trade: {trade_data['side']} {trade_data['symbol']}", 5000)

    def on_position_closed(self, symbol: str):
        """Handle position closed event"""
        self.logger.info(f"Position closed: {symbol}")
        self.status_bar.showMessage(f"Position closed: {symbol}", 3000)

        # Update risk dashboard when positions change
        self.update_risk_dashboard()

    def on_position_modified(self, symbol: str, modifications: Dict[str, Any]):
        """Handle position modified event"""
        self.logger.info(f"Position modified: {symbol} - {modifications}")
        self.status_bar.showMessage(f"Position modified: {symbol}", 3000)

        # Update risk dashboard when positions change
        self.update_risk_dashboard()

    def update_risk_dashboard(self):
        """Update risk dashboard with current positions and account data"""
        if hasattr(self, 'risk_dashboard') and hasattr(self, 'position_manager'):
            positions_data = getattr(self.position_manager, 'positions', {})
            account_data = self.data_manager.get_account_data()
            balance = account_data.balance if account_data else 0.0
            self.risk_dashboard.update_risk_metrics(positions_data, balance)

    def on_symbol_selected(self, symbol: str):
        """Handle symbol selection"""
        self.logger.info(f"Symbol selected: {symbol}")
        # Update other widgets with selected symbol if needed

    def on_risk_limit_breached(self, metric: str, value: float):
        """Handle risk limit breach"""
        self.logger.warning(f"Risk limit breached: {metric} = {value}")

        # Show warning dialog
        QMessageBox.warning(
            self,
            "Risk Limit Breached",
            f"⚠️ Risk limit breached!\n\nMetric: {metric}\nValue: {value}\n\nPlease review your positions."
        )

        self.status_bar.showMessage(f"Risk limit breached: {metric}", 10000)

    def on_circuit_breaker(self, reason: str):
        """Handle circuit breaker activation"""
        self.logger.warning(f"Circuit breaker triggered: {reason}")

        # Show circuit breaker notification
        QMessageBox.warning(
            self,
            "Circuit Breaker Activated",
            f"🛑 Circuit breaker activated!\n\nReason: {reason}\n\nTrading has been temporarily halted."
        )

        self.status_bar.showMessage(f"Circuit breaker: {reason}", 10000)

    def on_market_data_updated(self, symbol: str, data: Dict[str, Any]):
        """Handle market data update"""
        # Update status bar with current price if it's the main symbol
        if symbol == "BTC/USDT:USDT":  # Main symbol
            price = data.get('price', 0)
            self.status_bar.showMessage(f"BTC: ${price:.2f}", 2000)

    def on_account_updated(self, account_data: Dict[str, Any]):
        """Handle account data update"""
        balance = account_data.get('balance', 0)
        daily_pnl = account_data.get('daily_pnl', 0)

        self.status_balance.setText(f"Balance: ${balance:.2f}")

        pnl_text = f"Daily P&L: ${daily_pnl:+.2f}"
        self.status_pnl.setText(pnl_text)

        if daily_pnl >= 0:
            self.status_pnl.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_pnl.setStyleSheet("color: red; font-weight: bold;")

        # Update risk dashboard with real account data
        if hasattr(self, 'risk_dashboard') and hasattr(self, 'position_manager'):
            positions_data = getattr(self.position_manager, 'positions', {})
            self.risk_dashboard.update_risk_metrics(positions_data, balance)

    def on_system_status_updated(self, status_data: Dict[str, Any]):
        """Handle system status update"""
        connection_status = status_data.get('connection_status', 'disconnected')

        if connection_status == 'connected':
            self.connection_status.setText("🟢 Connected")
            self.connection_status.setStyleSheet("color: green; font-weight: bold;")
            self.status_connection.setText("🟢 Connected")
        else:
            self.connection_status.setText("🔴 Disconnected")
            self.connection_status.setStyleSheet("color: red; font-weight: bold;")
            self.status_connection.setText("🔴 Disconnected")

        # Update system monitor
        cpu_usage = status_data.get('cpu_usage', 0)
        memory_usage = status_data.get('memory_usage', 0)
        latency = status_data.get('network_latency', 0)

        self.cpu_bar.setValue(int(cpu_usage))
        self.memory_bar.setValue(int(memory_usage))
        self.latency_label.setText(f"{latency:.1f}ms")

        # Color code latency
        if latency < 50:
            self.latency_label.setStyleSheet("font-weight: bold; color: green;")
        elif latency < 100:
            self.latency_label.setStyleSheet("font-weight: bold; color: orange;")
        else:
            self.latency_label.setStyleSheet("font-weight: bold; color: red;")

    def on_alert_triggered(self, level: str, title: str, message: str):
        """Handle alert notification"""
        self.logger.warning(f"Alert [{level}] {title}: {message}")

        if level == "ERROR":
            QMessageBox.critical(self, title, message)
        elif level == "WARNING":
            QMessageBox.warning(self, title, message)
        else:
            self.status_bar.showMessage(f"{title}: {message}", 5000)

    # Action Methods
    @require_auth
    def start_trading(self, checked=False):
        """Start trading"""
        if hasattr(self, 'trading_controls'):
            self.trading_controls.start_trading(checked)

    @require_auth
    def stop_trading(self, checked=False):
        """Stop trading"""
        if hasattr(self, 'trading_controls'):
            self.trading_controls.stop_trading(checked)

    @admin_required
    def emergency_stop(self, checked=False):
        """Emergency stop"""
        if hasattr(self, 'trading_controls'):
            self.trading_controls.emergency_stop()

    def emergency_stop_shortcut(self):
        """Emergency stop via keyboard shortcut"""
        # Double confirmation for keyboard shortcut
        reply = QMessageBox.question(
            self,
            'Emergency Stop Shortcut',
            '🛑 Emergency stop activated via keyboard!\n\nThis will immediately halt all trading.\n\nConfirm?',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.emergency_stop()

    def toggle_trading(self):
        """Toggle trading state"""
        if self.start_action.isEnabled():
            self.start_trading()
        else:
            self.stop_trading()

    def refresh_all_data(self):
        """Refresh all data"""
        self.logger.info("Refreshing all dashboard data...")

        # Refresh market data
        if hasattr(self, 'market_data'):
            self.market_data.refresh_data()

        # Refresh positions
        if hasattr(self, 'position_manager'):
            self.position_manager.refresh_positions()

        # Force data manager update
        self.data_manager.update_market_data()
        self.data_manager.update_account_data()
        self.data_manager.update_system_status()

        self.status_bar.showMessage("All data refreshed", 3000)

    def toggle_theme(self):
        """Toggle between light and dark theme"""
        new_theme = "dark" if self.current_theme == "light" else "light"
        self.apply_theme(new_theme)

    def apply_theme(self, theme: str):
        """Apply theme to dashboard"""
        self.current_theme = theme
        self.settings.setValue("theme", theme)

        if theme == "dark":
            self.setStyleSheet(self.get_dark_theme_stylesheet())
        else:
            self.setStyleSheet(self.get_light_theme_stylesheet())

        self.theme_changed.emit(theme)
        self.logger.info(f"Theme changed to: {theme}")

    def update_status(self):
        """Update status indicators"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.status_update.setText(f"Last Update: {current_time}")

    def save_layout(self):
        """Save current layout"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())
        self.settings.setValue("splitter", self.main_splitter.saveState())

    def restore_layout(self):
        """Restore saved layout"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

        window_state = self.settings.value("windowState")
        if window_state:
            self.restoreState(window_state)

        splitter_state = self.settings.value("splitter")
        if splitter_state:
            self.main_splitter.restoreState(splitter_state)

    # Menu Actions
    def new_strategy(self):
        """Create new strategy"""
        QMessageBox.information(self, "New Strategy", "New strategy creation - Feature coming soon!")

    def open_strategy(self):
        """Open existing strategy"""
        QMessageBox.information(self, "Open Strategy", "Strategy loading - Feature coming soon!")

    def export_data(self):
        """Export trading data"""
        QMessageBox.information(self, "Export Data", "Data export - Feature coming soon!")

    def show_settings(self):
        """Show settings dialog"""
        QMessageBox.information(self, "Settings", "Settings dialog - Feature coming soon!")

    def show_risk_settings(self):
        """Show risk settings dialog"""
        QMessageBox.information(self, "Risk Settings", "Risk settings - Feature coming soon!")

    def show_documentation(self):
        """Show documentation"""
        QMessageBox.information(self, "Documentation", "Opening documentation - Feature coming soon!")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Epinnox v7",
            """
            <h2>Epinnox v7 Production Dashboard</h2>
            <p><b>Version:</b> 7.0.0</p>
            <p><b>Build Date:</b> June 2025</p>
            <p><b>Description:</b> Advanced LLM-powered cryptocurrency trading system with comprehensive risk management and real-time monitoring.</p>

            <h3>Features:</h3>
            <ul>
                <li>Real-time market data and charting</li>
                <li>Advanced position management</li>
                <li>Comprehensive risk monitoring</li>
                <li>Circuit breaker protection</li>
                <li>Role-based access control</li>
                <li>Production-grade security</li>
            </ul>

            <p><b>Developed by:</b> Epinnox Team</p>
            <p><b>Powered by:</b> Augment Agent</p>
            """
        )

    def logout(self):
        """Logout current user"""
        reply = QMessageBox.question(
            self,
            'Logout Confirmation',
            'Are you sure you want to logout?\n\nThis will stop all trading activities.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop trading first
            if hasattr(self, 'trading_controls'):
                self.trading_controls.stop_trading()

            # Stop data updates
            self.data_manager.stop_updates()

            # Save layout
            self.save_layout()

            # Logout
            session_manager.logout()
            self.logger.info("User logged out from dashboard")

            # Close dashboard
            self.close()

    # Theme Stylesheets
    def get_light_theme_stylesheet(self) -> str:
        """Get light theme stylesheet"""
        return """
            QMainWindow {
                background-color: #f5f5f5;
                color: #333333;
            }

            QWidget {
                background-color: #ffffff;
                color: #333333;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #ffffff;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #333333;
            }

            QFrame[frameShape="4"] {
                border: 1px solid #cccccc;
                background-color: #f9f9f9;
            }

            QPushButton {
                background-color: #e0e0e0;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 6px 12px;
                color: #333333;
            }

            QPushButton:hover {
                background-color: #d0d0d0;
                border-color: #999999;
            }

            QPushButton:pressed {
                background-color: #c0c0c0;
            }

            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: #ffffff;
            }

            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 8px 16px;
                margin-right: 2px;
            }

            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: 2px solid #2196F3;
            }

            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f5f5f5;
            }

            QHeaderView::section {
                background-color: #f0f0f0;
                border: none;
                border-bottom: 2px solid #2196F3;
                padding: 6px;
                font-weight: bold;
            }

            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #f0f0f0;
            }

            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }

            QStatusBar {
                background-color: #f0f0f0;
                border-top: 1px solid #cccccc;
            }

            QMenuBar {
                background-color: #f0f0f0;
                border-bottom: 1px solid #cccccc;
            }

            QMenuBar::item:selected {
                background-color: #e0e0e0;
            }

            QToolBar {
                background-color: #f0f0f0;
                border-bottom: 1px solid #cccccc;
                spacing: 3px;
            }
        """

    def get_dark_theme_stylesheet(self) -> str:
        """Get dark theme stylesheet"""
        return """
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }

            QWidget {
                background-color: #3c3c3c;
                color: #ffffff;
            }

            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #3c3c3c;
                color: #ffffff;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #ffffff;
            }

            QFrame[frameShape="4"] {
                border: 1px solid #555555;
                background-color: #404040;
            }

            QPushButton {
                background-color: #555555;
                border: 1px solid #777777;
                border-radius: 4px;
                padding: 6px 12px;
                color: #ffffff;
            }

            QPushButton:hover {
                background-color: #666666;
                border-color: #888888;
            }

            QPushButton:pressed {
                background-color: #444444;
            }

            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }

            QTabBar::tab {
                background-color: #505050;
                border: 1px solid #555555;
                padding: 8px 16px;
                margin-right: 2px;
                color: #ffffff;
            }

            QTabBar::tab:selected {
                background-color: #3c3c3c;
                border-bottom: 2px solid #2196F3;
            }

            QTableWidget {
                gridline-color: #555555;
                background-color: #3c3c3c;
                alternate-background-color: #404040;
                color: #ffffff;
            }

            QHeaderView::section {
                background-color: #505050;
                border: none;
                border-bottom: 2px solid #2196F3;
                padding: 6px;
                font-weight: bold;
                color: #ffffff;
            }

            QProgressBar {
                border: 2px solid #555555;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #404040;
                color: #ffffff;
            }

            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }

            QStatusBar {
                background-color: #505050;
                border-top: 1px solid #555555;
                color: #ffffff;
            }

            QMenuBar {
                background-color: #505050;
                border-bottom: 1px solid #555555;
                color: #ffffff;
            }

            QMenuBar::item:selected {
                background-color: #666666;
            }

            QToolBar {
                background-color: #505050;
                border-bottom: 1px solid #555555;
                spacing: 3px;
                color: #ffffff;
            }

            QLabel {
                color: #ffffff;
            }

            QComboBox {
                background-color: #555555;
                border: 1px solid #777777;
                border-radius: 4px;
                padding: 4px;
                color: #ffffff;
            }

            QComboBox::drop-down {
                border: none;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #ffffff;
            }

            QSpinBox, QDoubleSpinBox {
                background-color: #555555;
                border: 1px solid #777777;
                border-radius: 4px;
                padding: 4px;
                color: #ffffff;
            }

            QTextEdit {
                background-color: #404040;
                border: 1px solid #555555;
                color: #ffffff;
            }
        """

    # Window Events
    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(
            self,
            'Close Dashboard',
            'Are you sure you want to close the dashboard?\n\nThis will stop all trading activities.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop trading
            if hasattr(self, 'trading_controls'):
                self.trading_controls.stop_trading()

            # Stop data updates
            self.data_manager.stop_updates()

            # Save layout
            self.save_layout()

            self.logger.info("Dashboard closed")
            event.accept()
        else:
            event.ignore()


def main():
    """Main entry point for the production dashboard"""
    app = QApplication(sys.argv)
    app.setApplicationName("Epinnox v7 Production Dashboard")
    app.setApplicationVersion("7.0")

    # Load configuration
    try:
        config = ConfigValidator.load_and_validate("config/scalper_config.yaml")
    except Exception as e:
        QMessageBox.critical(None, "Configuration Error", f"Failed to load configuration: {str(e)}")
        sys.exit(1)

    # Initialize database with authentication tables
    from database.database_manager import get_database_manager
    from auth.models import Base

    try:
        db_manager = get_database_manager(config)
        Base.metadata.create_all(db_manager.engine)
        logger.info("Authentication tables initialized")
    except Exception as e:
        logger.error(f"Failed to initialize authentication: {str(e)}")
        QMessageBox.critical(None, "Database Error", f"Failed to initialize database: {str(e)}")
        sys.exit(1)

    # Show login dialog
    if not show_login_dialog(config):
        sys.exit(0)  # User cancelled login

    # Show splash screen
    try:
        splash_pix = QPixmap("assets/splash.png")
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        splash.show()
        app.processEvents()

        import time
        time.sleep(2)

        splash.close()
    except:
        pass  # No splash screen available

    # Create and show dashboard
    dashboard = ProductionDashboard(config)
    dashboard.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()