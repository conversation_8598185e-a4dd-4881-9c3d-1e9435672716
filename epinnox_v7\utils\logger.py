import sys
from datetime import datetime, timedelta
from pathlib import Path
import os
from loguru import logger
import yaml

def setup_logger(config_input):
    """
    Configure loguru logger with settings from config
    
    Parameters
    ----------
    config_input : Union[str, dict]
        Either a path to config file or config dictionary
    """
    # Get config dict
    if isinstance(config_input, str):
        with open(config_input, 'r') as f:
            config = yaml.safe_load(f)
    else:
        config = config_input
    
    log_config = config.get('logging', {})
    
    # Get log settings with defaults
    log_file = log_config.get('file', 'logs/scalper.log')
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Remove default logger
    logger.remove()
    
    # Add file logger with rotation
    logger.add(
        log_file,
        rotation=f"{log_config.get('max_size', 100)} MB",
        retention=log_config.get('backup_count', 5),
        level=log_config.get('level', 'INFO'),
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add stdout logger for console output with more visible format
    logger.add(
        sys.stderr,
        colorize=True,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <white>{message}</white>"
    )
    
    logger.info("Logger initialized")
    return logger

def get_logger():
    """Get the configured logger instance"""
    return logger

# Convenience functions for different log levels with color coding
def log_trade(msg: str):
    """Log trade-related messages in blue"""
    logger.info(f"💰 {msg}")

def log_signal(msg: str):
    """Log signal/strategy messages in yellow"""
    logger.info(f"🎯 {msg}")

def log_risk(msg: str):
    """Log risk-related messages in red"""
    logger.warning(f"⚠️ {msg}")

def log_performance(msg: str):
    """Log performance metrics in green"""
    logger.info(f"📈 {msg}")

def log_error(msg: str, exc_info=True):
    """Log errors in red with full traceback"""
    logger.exception(f"❌ {msg}") if exc_info else logger.error(f"❌ {msg}")

def prune_logs(log_dir='logs', retention_days=7):
    """
    Prune log files older than retention period
    
    Parameters
    ----------
    log_dir : str
        Directory where log files are stored
    retention_days : int
        Number of days to retain logs
    """
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    for log_file in Path(log_dir).rglob('*.log'):
        if datetime.fromtimestamp(log_file.stat().st_mtime) < cutoff_date:
            os.remove(log_file)
            logger.info(f"Removed old log file: {log_file}")

def prune_old_logs(config, max_age_days=7):
    """
    Delete log files older than specified number of days
    
    Parameters
    ----------
    config : dict
        Config dictionary containing logging paths
    max_age_days : int
        Maximum age of log files in days
    """
    try:
        log_dir = Path(config['logging']['log_dir'])
        if not log_dir.exists():
            return
            
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        # Find and delete old log files
        deleted_count = 0
        for log_file in log_dir.glob('*.log*'):
            # Skip the current main log file
            if log_file.name == 'scalper.log':
                continue
                
            # Get file modification time
            mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            
            # Delete if older than cutoff
            if mtime < cutoff_date:
                log_file.unlink()
                deleted_count += 1
                
        # Also check for old JSONL files
        for log_file in log_dir.glob('*.jsonl*'):
            mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
            if mtime < cutoff_date:
                log_file.unlink()
                deleted_count += 1
                
        if deleted_count > 0:
            logger.info(f"Log pruning: removed {deleted_count} old log files")
            
    except Exception as e:
        logger.error(f"Error during log pruning: {str(e)}")
