from typing import Dict, Optional, Tuple
from utils.enums import ActionType, RiskLevel
from utils.logger import get_logger, log_risk

class RiskGuard:
    """Validates trades against risk parameters and maintains risk limits"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = get_logger()
        
        # Risk limits from config
        self.max_drawdown_pct = config['risk_limits']['max_drawdown_pct']
        self.min_liq_buffer_pct = config['risk_limits']['min_liquidation_buffer_pct']
        self.min_win_rate = config['risk_limits']['min_win_rate']
        
        # Position sizing
        self.position_size_pct = config['position_size_pct']
        self.leverage = config['leverage']
        
        # Operation mode
        self.dry_run = config.get('dry_run', True)
        self.live_mode = config.get('live_mode', False)
        
        # Maximum allowed action size in live mode
        self.max_live_position_size = config.get('max_live_position_size_usd', 50)
        
    def check_market_quality(self, analysis: Dict) -> Tuple[bool, str]:
        """Check if market conditions are suitable for trading"""
        
        # Get manipulation metrics
        manipulation = analysis.get('manipulation_metrics', {})
        if manipulation.get('overall_score', 1.0) > 0.3:
            return False, "High manipulation risk detected"
            
        # Check order flow quality
        order_flow = analysis.get('order_flow', {})
        if order_flow:
            # Check for excessive institutional activity
            if order_flow.get('large_orders_count', 0) > 5:
                return False, "High institutional activity - potential manipulation"
                
            # Check for balanced market
            buying_pressure = order_flow.get('buying_pressure', 0)
            selling_pressure = order_flow.get('selling_pressure', 0)
            if max(buying_pressure, selling_pressure) / (min(buying_pressure, selling_pressure) + 1e-10) > 3:
                return False, "Imbalanced order flow - wait for stabilization"
                
            # Check for clear directional bias
            if 0.4 <= order_flow.get('delta_momentum', 0.5) <= 0.6:
                return False, "No clear directional bias"
        
        # Market structure checks
        if 'market_structure' in analysis:
            structure = analysis['market_structure']
            if structure.get('trend') == 'sideways' and structure.get('strength', 0) < 0.3:
                return False, "Choppy market conditions"
        
        # Volume quality check
        natural_trading = manipulation.get('natural_trading', {})
        if natural_trading.get('volume_consistency', 0) < 0.5:
            return False, "Inconsistent volume patterns"
        
        # Spread and liquidity check
        if natural_trading.get('spread_stability', 0) < 0.4:
            return False, "Unstable spreads - poor liquidity"
        
        return True, "Market conditions acceptable"

    def validate_trade(self,
                      action: Dict,
                      market_data: Dict,
                      position_data: Dict,
                      strategy_state: Dict) -> Tuple[bool, Optional[str]]:
        """
        Validate a proposed trade against all risk parameters
        
        Returns
        -------
        Tuple[bool, Optional[str]]
            (is_valid, reason_if_invalid)
        """
        try:
            # Verify we have all required fields
            if not action:
                return False, "Empty action data"
                
            # Validate required action fields exist
            required_fields = {'action', 'confidence'}
            if action['action'] not in [ActionType.HOLD.value, ActionType.CLOSE.value]:
                required_fields.update({'entry_price', 'stop_loss'})
                
            missing_fields = required_fields - set(action.keys())
            if missing_fields:
                return False, f"Missing required fields: {', '.join(missing_fields)}"
            
            # Validate market data has required fields
            if not market_data or 'price' not in market_data:
                return False, "Invalid or missing market data"
                
            # Skip validation for hold actions
            if action['action'] == ActionType.HOLD.value:
                return True, None
                
            # Extra safety for live mode
            if not self.dry_run and self.live_mode:
                # Verify position size limit in live mode
                if action.get('position_size', 0) > self.max_live_position_size:
                    return False, f"Position size exceeds live mode limit of {self.max_live_position_size} USD"
                    
                # Additional safety checks for live mode
                confidence_threshold = self.config.get('live_mode_confidence_threshold', 0.8)
                if action.get('confidence', 0) < confidence_threshold:
                    return False, f"Confidence too low for live trading: {action.get('confidence', 0):.2f} < {confidence_threshold}"
            
            # 1. Check drawdown limit
            current_drawdown = strategy_state.get('current_drawdown', 0)
            if current_drawdown >= self.max_drawdown_pct:
                return False, f"Max drawdown limit reached: {current_drawdown:.2f}%"
                
            # 2. Check liquidation buffer for new trades
            if action['action'] in [ActionType.LONG.value, ActionType.SHORT.value]:
                liq_buffer = self._calculate_liquidation_buffer(
                    action, market_data['price'], self.leverage
                )
                if liq_buffer < self.min_liq_buffer_pct:
                    return False, f"Insufficient liquidation buffer: {liq_buffer:.2f}%"
                    
            # 3. Validate stop loss
            if not self._validate_stop_loss(action, market_data):
                return False, "Invalid stop loss placement"
                
            # 4. Check risk level restrictions
            if strategy_state['risk_mode'] == RiskLevel.EXTREME.value:
                if action['action'] != ActionType.CLOSE.value:
                    return False, "New positions blocked due to extreme risk level"
                    
            # 5. Validate position sizing
            if not self._validate_position_size(action, market_data):
                return False, "Invalid position size"
                
            return True, None
            
        except Exception as e:
            log_risk(f"Error in trade validation: {str(e)}")
            return False, f"Validation error: {str(e)}"
            
    def _calculate_liquidation_buffer(self,
                                    action: Dict,
                                    current_price: float,
                                    leverage: float) -> float:
        """Calculate distance to liquidation as percentage"""
        entry_price = action['entry_price']
        
        if action['action'] == ActionType.LONG.value:
            liq_price = entry_price * (1 - (1 / leverage))
            buffer_pct = ((entry_price - liq_price) / entry_price) * 100
        else:  # SHORT
            liq_price = entry_price * (1 + (1 / leverage))
            buffer_pct = ((liq_price - entry_price) / entry_price) * 100
            
        return buffer_pct
        
    def _validate_stop_loss(self, action: Dict, market_data: Dict) -> bool:
        """Validate stop loss placement"""
        if action['action'] in [ActionType.HOLD.value, ActionType.CLOSE.value]:
            return True
            
        entry_price = action['entry_price']
        stop_loss = action['stop_loss']
        
        # Calculate SL distance as percentage
        sl_pct = abs(entry_price - stop_loss) / entry_price * 100
        
        # Must be greater than minimum
        if sl_pct < market_data['min_sl_distance_pct']:
            return False
            
        # Validate SL direction
        if action['action'] == ActionType.LONG.value:
            return stop_loss < entry_price
        else:  # SHORT
            return stop_loss > entry_price
            
    def _validate_position_size(self, action: Dict, market_data: Dict) -> bool:
        """Validate position size against limits"""
        if action['action'] in [ActionType.HOLD.value, ActionType.CLOSE.value]:
            return True
            
        # Get position size in quote currency
        pos_size = abs(action['position_size'])
        
        # Calculate allowed size based on config percentage
        # Note: In real implementation, would need account balance from exchange
        max_size = market_data.get('account_balance', 1000) * self.position_size_pct
        
        return pos_size <= max_size
        
    def adjust_position_size(self,
                           base_size: float,
                           risk_level: RiskLevel,
                           win_rate: float) -> float:
        """Adjust position size based on risk factors"""
        
        # Start with configured size
        adjusted_size = base_size
        
        # Risk level adjustments
        risk_multipliers = {
            RiskLevel.LOW: 1.0,
            RiskLevel.MEDIUM: 0.8,
            RiskLevel.HIGH: 0.5,
            RiskLevel.EXTREME: 0.25
        }
        
        # Apply risk multiplier
        adjusted_size *= risk_multipliers[risk_level]
        
        # Win rate adjustment
        if win_rate < self.min_win_rate + 0.1:  # Within 10% of minimum
            adjusted_size *= 0.5
            
        return adjusted_size
