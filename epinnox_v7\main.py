import asyncio
import os
import signal
import argparse
from pathlib import Path
from utils.logger import setup_logger, get_logger
from utils.config_validator import ConfigValidator
from core.scalper_loop import Scalper<PERSON>oop
from typing import Optional, Dict

def parse_args() -> argparse.Namespace:
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Epinnox v7 LLM-based Crypto Futures Scalper"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in simulation mode without real trades"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="config/scalper_config.yaml",
        help="Path to configuration file"
    )
    
    return parser.parse_args()
    
class EpinnoxScalper:
    def __init__(self, config_path: str = "config/scalper_config.yaml", dry_run: bool = False):
        # Load and validate config first
        self.config = ConfigValidator.load_and_validate(config_path)
        
        # Override live mode if dry run
        if dry_run:
            self.config['live_mode'] = False
        
        # Initialize logger
        self.logger = setup_logger(self.config)
        if dry_run:
            self.logger.info("Running in DRY RUN mode - trades will be simulated")
            
        self.loop: Optional[ScalperLoop] = None
        self.shutdown_event = asyncio.Event()
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}")
        if not self.shutdown_event.is_set():
            self.shutdown_event.set()
            
    async def shutdown(self):
        """Handle graceful shutdown"""
        if self.loop:
            self.logger.info("Initiating graceful shutdown...")
            await self.loop.shutdown()
            self.logger.info("Shutdown complete")
            
    async def run(self):
        """Run the trading bot"""
        try:
            # Setup signal handlers
            for sig in (signal.SIGTERM, signal.SIGINT):
                signal.signal(sig, self.signal_handler)
            
            # Ensure we're in the correct directory
            os.chdir(Path(__file__).parent)
            
            self.logger.info("Starting Epinnox v7 LLM Scalper")
            
            # Create and start trading loop
            self.loop = ScalperLoop(self.config)
            await self.loop.initialize()
            
            # Run until shutdown signal
            await self.loop.start(self.shutdown_event)
            
        except Exception as e:
            self.logger.error(f"Critical error: {str(e)}")
            raise
        finally:
            await self.shutdown()
            
async def main():
    """Main entry point"""
    # Parse command line arguments
    args = parse_args()
    
    # Create and run scalper
    scalper = EpinnoxScalper(config_path=args.config, dry_run=args.dry_run)
    await scalper.run()
    
if __name__ == "__main__":
    try:
        # Run main function
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown signal received. Exiting gracefully...")
    except Exception as e:
        print(f"\nCritical error: {str(e)}")
        raise
