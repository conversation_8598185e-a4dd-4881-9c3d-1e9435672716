import asyncio
import os
import signal
import argparse
from pathlib import Path
from utils.logger import setup_logger, get_logger
from utils.config_validator import ConfigValida<PERSON>
from core.scalper_loop import Scalper<PERSON>oop
from typing import Optional, Dict

def parse_args() -> argparse.Namespace:
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Epinnox v7 LLM-based Crypto Futures Scalper"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in simulation mode without real trades"
    )

    parser.add_argument(
        "--config",
        type=str,
        default="config/scalper_config.yaml",
        help="Path to configuration file"
    )

    parser.add_argument(
        "--gui",
        action="store_true",
        help="Launch GUI dashboard interface"
    )

    parser.add_argument(
        "--backtest",
        action="store_true",
        help="Run demonstration backtest"
    )

    return parser.parse_args()


def launch_dashboard():
    """Launch the PyQt5 dashboard interface"""
    try:
        from gui.dashboard import main as dashboard_main
        print("🚀 Launching Epinnox v7 Dashboard...")
        dashboard_main()
    except ImportError as e:
        print(f"❌ Error importing GUI components: {e}")
        print("📦 Please install required dependencies:")
        print("   pip install PyQt5 matplotlib plotly")
        exit(1)
    except Exception as e:
        print(f"❌ Error launching dashboard: {e}")
        import traceback
        traceback.print_exc()
        exit(1)


async def run_backtest():
    """Run a quick backtest demonstration"""
    try:
        from strategy_builder.backtester import Backtester

        print("🧪 Running demonstration backtest...")
        backtester = Backtester()

        # Fetch sample data
        print("📊 Fetching DOGE/USDT data...")
        data = await backtester.fetch_historical_data(
            symbol='DOGE/USDT:USDT',
            timeframe='1m',
            days=1,
            exchange_id='binance'
        )

        if data is None or len(data) < 100:
            print("❌ Insufficient data for backtest")
            return

        # Load strategy
        strategy = backtester.load_strategy('strategy_builder/strategies/simple_momentum.yaml')

        # Run backtest
        print("🔄 Running backtest...")
        results = await backtester.run_backtest(
            symbol='DOGE/USDT:USDT',
            ohlcv_data=data,
            strategy_config=strategy,
            use_llm=False,
            initial_balance=500.0,
            leverage=20
        )

        # Display results
        print("\n📈 Backtest Results:")
        print(f"   Total trades: {results.total_trades}")
        print(f"   Win rate: {results.win_rate:.2f}%")
        print(f"   Total PnL: ${results.total_pnl:.2f} ({results.total_pnl_pct:.2f}%)")
        print(f"   Max drawdown: {results.max_drawdown_pct:.2f}%")
        print(f"   Sharpe ratio: {results.sharpe_ratio:.2f}")

        # Analyze results
        analysis = backtester.analyze_results(results)
        print(f"   Performance grade: {analysis['performance_grade']}")

        print("\n✅ Backtest completed! Check logs/backtest_results/ for detailed results.")

    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()


class EpinnoxScalper:
    def __init__(self, config_path: str = "config/scalper_config.yaml", dry_run: bool = False):
        # Load and validate config first
        self.config = ConfigValidator.load_and_validate(config_path)
        
        # Override live mode if dry run
        if dry_run:
            self.config['live_mode'] = False
        
        # Initialize logger
        self.logger = setup_logger(self.config)
        if dry_run:
            self.logger.info("Running in DRY RUN mode - trades will be simulated")
            
        self.loop: Optional[ScalperLoop] = None
        self.shutdown_event = asyncio.Event()
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}")
        if not self.shutdown_event.is_set():
            self.shutdown_event.set()
            
    async def shutdown(self):
        """Handle graceful shutdown"""
        if self.loop:
            self.logger.info("Initiating graceful shutdown...")
            await self.loop.shutdown()
            self.logger.info("Shutdown complete")
            
    async def run(self):
        """Run the trading bot"""
        try:
            # Setup signal handlers
            for sig in (signal.SIGTERM, signal.SIGINT):
                signal.signal(sig, self.signal_handler)
            
            # Ensure we're in the correct directory
            os.chdir(Path(__file__).parent)
            
            self.logger.info("Starting Epinnox v7 LLM Scalper")
            
            # Create and start trading loop
            self.loop = ScalperLoop(self.config)
            await self.loop.initialize()
            
            # Run until shutdown signal
            await self.loop.start(self.shutdown_event)
            
        except Exception as e:
            self.logger.error(f"Critical error: {str(e)}")
            raise
        finally:
            await self.shutdown()
            
async def main():
    """Main entry point"""
    # Parse command line arguments
    args = parse_args()

    # Handle GUI mode
    if args.gui:
        launch_dashboard()
        return

    # Handle backtest mode
    if args.backtest:
        await run_backtest()
        return

    # Default: Create and run scalper
    scalper = EpinnoxScalper(config_path=args.config, dry_run=args.dry_run)
    await scalper.run()
    
if __name__ == "__main__":
    try:
        # Run main function
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown signal received. Exiting gracefully...")
    except Exception as e:
        print(f"\nCritical error: {str(e)}")
        raise
