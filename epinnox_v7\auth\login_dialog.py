"""
Login Dialog for Epinnox v7 GUI

Provides secure login interface with session management.
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QPushButton, QLabel, QCheckBox,
    QMessageBox, QApplication, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

from .authentication import AuthenticationManager, session_manager
from .models import UserRole
from database.database_manager import get_database_manager
from utils.logger import get_logger

logger = get_logger()


class LoginDialog(QDialog):
    """Secure login dialog with authentication"""
    
    login_successful = pyqtSignal(str)  # Emits username on successful login
    
    def __init__(self, config: dict, parent=None):
        super().__init__(parent)
        self.config = config
        self.auth_manager = None
        self.setup_ui()
        self.setup_auth()
    
    def setup_auth(self):
        """Initialize authentication manager"""
        try:
            db_manager = get_database_manager(self.config)
            with db_manager.get_session() as session:
                self.auth_manager = AuthenticationManager(session)
                session_manager.set_auth_manager(self.auth_manager)
        except Exception as e:
            logger.error(f"Failed to initialize authentication: {str(e)}")
            QMessageBox.critical(
                self,
                "Authentication Error",
                f"Failed to initialize authentication system:\n{str(e)}"
            )
    
    def setup_ui(self):
        """Setup the login dialog UI"""
        self.setWindowTitle("Epinnox v7 - Login")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        # Main layout
        layout = QVBoxLayout()
        layout.setSpacing(20)
        
        # Header
        header_layout = QVBoxLayout()
        
        title_label = QLabel("Epinnox v7")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        
        subtitle_label = QLabel("LLM-Powered Crypto Trading System")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #666; font-size: 12px;")
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        # Login form
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter username")
        self.username_input.returnPressed.connect(self.login)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Enter password")
        self.password_input.returnPressed.connect(self.login)
        
        self.remember_checkbox = QCheckBox("Remember me")
        
        form_layout.addRow("Username:", self.username_input)
        form_layout.addRow("Password:", self.password_input)
        form_layout.addRow("", self.remember_checkbox)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton("Login")
        self.login_button.setDefault(True)
        self.login_button.clicked.connect(self.login)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        
        self.create_user_button = QPushButton("Create User")
        self.create_user_button.clicked.connect(self.show_create_user_dialog)
        
        button_layout.addWidget(self.create_user_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red; font-size: 11px;")
        
        # Add all to main layout
        layout.addLayout(header_layout)
        layout.addWidget(separator)
        layout.addLayout(form_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.status_label)
        layout.addStretch()
        
        self.setLayout(layout)
        
        # Focus on username input
        self.username_input.setFocus()
    
    def login(self):
        """Handle login attempt"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_status("Please enter both username and password", error=True)
            return
        
        if not self.auth_manager:
            self.show_status("Authentication system not available", error=True)
            return
        
        # Attempt login
        try:
            success, message = session_manager.login(
                username=username,
                password=password,
                ip_address="127.0.0.1",  # Local GUI access
                user_agent="Epinnox v7 GUI"
            )
            
            if success:
                logger.info(f"Successful login: {username}")
                self.login_successful.emit(username)
                self.accept()
            else:
                self.show_status(message, error=True)
                self.password_input.clear()
                self.password_input.setFocus()
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            self.show_status(f"Login error: {str(e)}", error=True)
    
    def show_status(self, message: str, error: bool = False):
        """Show status message"""
        self.status_label.setText(message)
        if error:
            self.status_label.setStyleSheet("color: red; font-size: 11px;")
        else:
            self.status_label.setStyleSheet("color: green; font-size: 11px;")
    
    def show_create_user_dialog(self):
        """Show create user dialog"""
        dialog = CreateUserDialog(self.auth_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            self.show_status("User created successfully! Please login.", error=False)


class CreateUserDialog(QDialog):
    """Dialog for creating new users"""
    
    def __init__(self, auth_manager: AuthenticationManager, parent=None):
        super().__init__(parent)
        self.auth_manager = auth_manager
        self.setup_ui()
    
    def setup_ui(self):
        """Setup create user dialog UI"""
        self.setWindowTitle("Create New User")
        self.setFixedSize(350, 250)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # Form
        form_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Username (min 3 chars)")
        
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("Email address")
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Password (min 8 chars)")
        
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setPlaceholderText("Confirm password")
        
        form_layout.addRow("Username:", self.username_input)
        form_layout.addRow("Email:", self.email_input)
        form_layout.addRow("Password:", self.password_input)
        form_layout.addRow("Confirm:", self.confirm_password_input)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        create_button = QPushButton("Create User")
        create_button.clicked.connect(self.create_user)
        
        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(create_button)
        
        # Status
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red; font-size: 11px;")
        
        layout.addLayout(form_layout)
        layout.addLayout(button_layout)
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
    
    def create_user(self):
        """Create new user"""
        username = self.username_input.text().strip()
        email = self.email_input.text().strip()
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()
        
        # Validation
        if not all([username, email, password, confirm_password]):
            self.status_label.setText("All fields are required")
            return
        
        if password != confirm_password:
            self.status_label.setText("Passwords do not match")
            return
        
        # Create user
        try:
            success, message = self.auth_manager.create_user(
                username=username,
                email=email,
                password=password,
                role=UserRole.VIEWER  # Default role
            )
            
            if success:
                self.accept()
            else:
                self.status_label.setText(message)
                
        except Exception as e:
            self.status_label.setText(f"Error: {str(e)}")


def show_login_dialog(config: dict) -> bool:
    """
    Show login dialog and return True if login successful
    """
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    dialog = LoginDialog(config)
    result = dialog.exec_()
    
    return result == QDialog.Accepted
